'use client';

import { useEffect, useState, useRef } from 'react';
import { gsap } from 'gsap';
import { GSAPUtils } from '@/lib/gsap-utils';

interface PageTransitionProps {
  isPresent: boolean;
  onExitComplete?: () => void;
}

export default function PageTransition({ isPresent, onExitComplete }: PageTransitionProps) {
  const [isAnimating, setIsAnimating] = useState(true);
  const overlayRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const timelineRef = useRef<gsap.core.Timeline>();

  useEffect(() => {
    if (!overlayRef.current || !contentRef.current) return;

    // Kill any existing animations
    if (timelineRef.current) {
      timelineRef.current.kill();
    }

    // Create new timeline
    const tl = gsap.timeline({
      onComplete: () => {
        setIsAnimating(false);
        if (!isPresent && onExitComplete) {
          onExitComplete();
        }
      }
    });

    if (isPresent) {
      // Exit animation
      tl.to(overlayRef.current, {
        scaleY: 0,
        duration: 0.5,
        ease: "power3.inOut",
        transformOrigin: "top",
        force3D: true
      })
      .to(contentRef.current, {
        y: 0,
        opacity: 1,
        duration: 0.5,
        ease: "power3.inOut",
        force3D: true
      }, "-=0.3");
    } else {
      // Enter animation
      tl.set(overlayRef.current, { scaleY: 1, transformOrigin: "bottom" })
      .to(contentRef.current, {
        y: 100,
        opacity: 0,
        duration: 0.5,
        ease: "power3.inOut",
        force3D: true
      })
      .to(overlayRef.current, {
        scaleY: 1,
        duration: 0.5,
        ease: "power3.inOut",
        force3D: true
      }, "-=0.2");
    }

    timelineRef.current = tl;

    // Cleanup
    return () => {
      if (timelineRef.current) {
        timelineRef.current.kill();
      }
    };
  }, [isPresent, onExitComplete]);

  return (
    <>
      <div
        ref={overlayRef}
        className="fixed inset-0 z-[999] pointer-events-none"
        style={{
          background: 'linear-gradient(to right, #4F46E5, #7C3AED)',
          transform: 'scaleY(0)',
          transformOrigin: 'top'
        }}
      />

      {/* Sliding content effect */}
      <div
        ref={contentRef}
        className="fixed top-1/2 left-1/2 z-[1000] pointer-events-none"
        style={{
          transform: 'translate(-50%, -50%)',
          opacity: 0
        }}
      >
        <div className="text-white text-4xl font-bold tracking-wider">
          {isPresent ? 'Welcome' : 'See you soon!'}
        </div>
      </div>
    </>
  );
}