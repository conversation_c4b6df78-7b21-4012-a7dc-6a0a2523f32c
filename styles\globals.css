@tailwind base;
@tailwind components;
@tailwind utilities;

/* Add custom animation keyframes */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -30px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

@keyframes meshFloat {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 200px 200px;
  }
}

@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Add main dark theme */
:root {
  --background: 0 0% 3.9%;
  --foreground: 0 0% 98%;
  --primary: 266 100% 50%;
  --primary-foreground: 0 0% 98%;
  --secondary: 240 3.7% 15.9%;
  --secondary-foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --accent: 240 3.7% 15.9%;
  --accent-foreground: 0 0% 98%;
  --card: 240 10% 3.9%;
  --card-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --ring: 240 4.9% 83.9%;
}

/* Add utility classes for animations */
.animate-blob {
  animation-duration: 25s;
  animation-fill-mode: both;
  animation-iteration-count: infinite;
  animation-timing-function: ease-in-out;
}

.animate-pulse {
  animation: pulse 6s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.1;
  }
  50% {
    opacity: 0.2;
  }
}

/* Highlight animation for Full-Stack plans */
.highlight-pulse {
  animation: highlightPulse 2s ease-in-out infinite;
  position: relative;
  z-index: 10;
}

@keyframes highlightPulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(147, 51, 234, 0.7);
    border-color: rgba(147, 51, 234, 0.5);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(147, 51, 234, 0);
    border-color: rgba(147, 51, 234, 1);
  }
}

/* Add additional styling */
body {
  background-color: #050509;
  color: #f8fafc;
  overflow-x: hidden;
}