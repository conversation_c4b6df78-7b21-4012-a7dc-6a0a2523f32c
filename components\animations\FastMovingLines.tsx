'use client'

import React, { useEffect, useRef } from 'react'
import { gsap } from 'gsap'

interface FastMovingLinesProps {
  className?: string
  lineCount?: number
  speed?: number
  color?: string
}

const FastMovingLines: React.FC<FastMovingLinesProps> = ({
  className = '',
  lineCount = 15,
  speed = 2,
  color = '#8b5cf6'
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>()
  const gsapTimelineRef = useRef<gsap.core.Timeline>()
  const linesRef = useRef<Array<{
    x: number
    y: number
    length: number
    angle: number
    speed: number
    opacity: number
    thickness: number
  }>>([])

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    const resizeCanvas = () => {
      const rect = canvas.getBoundingClientRect()
      canvas.width = rect.width * window.devicePixelRatio
      canvas.height = rect.height * window.devicePixelRatio
      ctx.scale(window.devicePixelRatio, window.devicePixelRatio)
      canvas.style.width = rect.width + 'px'
      canvas.style.height = rect.height + 'px'
    }

    const initLines = () => {
      linesRef.current = []
      for (let i = 0; i < lineCount; i++) {
        linesRef.current.push({
          x: Math.random() * canvas.width / window.devicePixelRatio,
          y: Math.random() * canvas.height / window.devicePixelRatio,
          length: Math.random() * 100 + 50,
          angle: Math.random() * Math.PI * 2,
          speed: (Math.random() * speed + 0.5) * 2,
          opacity: Math.random() * 0.8 + 0.2,
          thickness: Math.random() * 2 + 1
        })
      }
    }

    const animate = () => {
      // Use hardware acceleration for better performance
      ctx.clearRect(0, 0, canvas.width / window.devicePixelRatio, canvas.height / window.devicePixelRatio)

      // Batch operations for better performance
      ctx.save()

      linesRef.current.forEach((line, index) => {
        // Update position with optimized calculations
        const cosAngle = Math.cos(line.angle)
        const sinAngle = Math.sin(line.angle)

        line.x += cosAngle * line.speed
        line.y += sinAngle * line.speed

        const canvasWidth = canvas.width / window.devicePixelRatio
        const canvasHeight = canvas.height / window.devicePixelRatio

        // Wrap around screen with optimized bounds checking
        if (line.x > canvasWidth + line.length) {
          line.x = -line.length
        } else if (line.x < -line.length) {
          line.x = canvasWidth + line.length
        }

        if (line.y > canvasHeight + line.length) {
          line.y = -line.length
        } else if (line.y < -line.length) {
          line.y = canvasHeight + line.length
        }

        // Create gradient for stringy effect with cached calculations
        const endX = line.x + cosAngle * line.length
        const endY = line.y + sinAngle * line.length

        const gradient = ctx.createLinearGradient(line.x, line.y, endX, endY)
        const opacityHex = Math.floor(line.opacity * 255).toString(16).padStart(2, '0')

        gradient.addColorStop(0, `${color}00`)
        gradient.addColorStop(0.3, `${color}${opacityHex}`)
        gradient.addColorStop(0.7, `${color}${opacityHex}`)
        gradient.addColorStop(1, `${color}00`)

        // Draw the line with optimized path
        ctx.beginPath()
        ctx.moveTo(line.x, line.y)
        ctx.lineTo(endX, endY)
        ctx.strokeStyle = gradient
        ctx.lineWidth = line.thickness
        ctx.lineCap = 'round'
        ctx.stroke()

        // Add controlled randomness to movement
        line.angle += (Math.random() - 0.5) * 0.015
        line.opacity += (Math.random() - 0.5) * 0.008
        line.opacity = Math.max(0.15, Math.min(0.85, line.opacity))
      })

      ctx.restore()
    }

    resizeCanvas()
    initLines()

    // Use GSAP ticker for better performance and 60fps targeting
    gsapTimelineRef.current = gsap.timeline({ repeat: -1 })
    gsapTimelineRef.current.to({}, {
      duration: 0.016, // ~60fps
      repeat: -1,
      ease: "none",
      onRepeat: animate
    })

    const handleResize = () => {
      resizeCanvas()
      initLines()
    }

    window.addEventListener('resize', handleResize)

    return () => {
      // Cleanup GSAP timeline
      if (gsapTimelineRef.current) {
        gsapTimelineRef.current.kill()
      }
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
      window.removeEventListener('resize', handleResize)
    }
  }, [lineCount, speed, color])

  return (
    <canvas
      ref={canvasRef}
      className={`absolute inset-0 pointer-events-none ${className}`}
      style={{
        width: '100%',
        height: '100%',
        transform: 'translate3d(0, 0, 0)',
        willChange: 'transform'
      }}
    />
  )
}

export default FastMovingLines
