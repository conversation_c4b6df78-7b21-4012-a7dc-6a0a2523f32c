/**
 * Performance Monitor Utility for Nexious AI Chatbot
 * 
 * This utility provides real-time performance monitoring for the chatbot interface,
 * tracking frame rates, scroll performance, and rendering optimization metrics.
 */

interface PerformanceMetrics {
  fps: number;
  frameTime: number;
  scrollPerformance: number;
  renderingTime: number;
  memoryUsage?: number;
}

interface PerformanceConfig {
  targetFPS: number;
  monitoringInterval: number;
  enableLogging: boolean;
  enableMemoryTracking: boolean;
}

class PerformanceMonitor {
  private config: PerformanceConfig;
  private frameCount: number = 0;
  private lastTime: number = 0;
  private frameStartTime: number = 0;
  private frameTimes: number[] = [];
  private scrollStartTime: number = 0;
  private renderStartTime: number = 0;
  private isMonitoring: boolean = false;
  private animationFrameId: number | null = null;
  private metricsCallback?: (metrics: PerformanceMetrics) => void;

  constructor(config: Partial<PerformanceConfig> = {}) {
    this.config = {
      targetFPS: 60,
      monitoringInterval: 1000, // 1 second
      enableLogging: false,
      enableMemoryTracking: true,
      ...config
    };
  }

  /**
   * Start performance monitoring
   */
  startMonitoring(callback?: (metrics: PerformanceMetrics) => void): void {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.metricsCallback = callback;
    this.frameCount = 0;
    this.lastTime = performance.now();
    this.frameTimes = [];

    this.monitorFrame();
  }

  /**
   * Stop performance monitoring
   */
  stopMonitoring(): void {
    this.isMonitoring = false;
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }

  /**
   * Monitor frame performance
   */
  private monitorFrame = (): void => {
    if (!this.isMonitoring) return;

    const currentTime = performance.now();
    const deltaTime = currentTime - this.frameStartTime;
    
    this.frameCount++;
    this.frameTimes.push(deltaTime);

    // Keep only recent frame times (last 60 frames)
    if (this.frameTimes.length > 60) {
      this.frameTimes.shift();
    }

    // Calculate metrics every monitoring interval
    if (currentTime - this.lastTime >= this.config.monitoringInterval) {
      const metrics = this.calculateMetrics(currentTime);
      
      if (this.metricsCallback) {
        this.metricsCallback(metrics);
      }

      if (this.config.enableLogging) {
        this.logMetrics(metrics);
      }

      // Reset counters
      this.frameCount = 0;
      this.lastTime = currentTime;
    }

    this.frameStartTime = currentTime;
    this.animationFrameId = requestAnimationFrame(this.monitorFrame);
  };

  /**
   * Calculate performance metrics
   */
  private calculateMetrics(currentTime: number): PerformanceMetrics {
    const timeElapsed = currentTime - this.lastTime;
    const fps = Math.round((this.frameCount * 1000) / timeElapsed);
    
    const avgFrameTime = this.frameTimes.length > 0 
      ? this.frameTimes.reduce((sum, time) => sum + time, 0) / this.frameTimes.length
      : 0;

    const metrics: PerformanceMetrics = {
      fps: Math.min(fps, this.config.targetFPS),
      frameTime: avgFrameTime,
      scrollPerformance: this.calculateScrollPerformance(),
      renderingTime: this.calculateRenderingTime()
    };

    // Add memory usage if enabled and available
    if (this.config.enableMemoryTracking && 'memory' in performance) {
      const memInfo = (performance as any).memory;
      metrics.memoryUsage = memInfo.usedJSHeapSize / (1024 * 1024); // MB
    }

    return metrics;
  }

  /**
   * Calculate scroll performance score
   */
  private calculateScrollPerformance(): number {
    // Return a score from 0-100 based on frame consistency
    if (this.frameTimes.length === 0) return 100;

    const targetFrameTime = 1000 / this.config.targetFPS; // 16.67ms for 60fps
    const variance = this.frameTimes.reduce((sum, time) => {
      return sum + Math.pow(time - targetFrameTime, 2);
    }, 0) / this.frameTimes.length;

    const standardDeviation = Math.sqrt(variance);
    const performanceScore = Math.max(0, 100 - (standardDeviation * 2));
    
    return Math.round(performanceScore);
  }

  /**
   * Calculate rendering time performance
   */
  private calculateRenderingTime(): number {
    return this.frameTimes.length > 0 
      ? Math.round(this.frameTimes[this.frameTimes.length - 1] * 100) / 100
      : 0;
  }

  /**
   * Mark the start of a scroll operation
   */
  markScrollStart(): void {
    this.scrollStartTime = performance.now();
  }

  /**
   * Mark the end of a scroll operation
   */
  markScrollEnd(): number {
    if (this.scrollStartTime === 0) return 0;
    const scrollTime = performance.now() - this.scrollStartTime;
    this.scrollStartTime = 0;
    return scrollTime;
  }

  /**
   * Mark the start of a render operation
   */
  markRenderStart(): void {
    this.renderStartTime = performance.now();
  }

  /**
   * Mark the end of a render operation
   */
  markRenderEnd(): number {
    if (this.renderStartTime === 0) return 0;
    const renderTime = performance.now() - this.renderStartTime;
    this.renderStartTime = 0;
    return renderTime;
  }

  /**
   * Log performance metrics to console
   */
  private logMetrics(metrics: PerformanceMetrics): void {
    console.group('🚀 Nexious Performance Metrics');
    console.log(`FPS: ${metrics.fps}/${this.config.targetFPS}`);
    console.log(`Frame Time: ${metrics.frameTime.toFixed(2)}ms`);
    console.log(`Scroll Performance: ${metrics.scrollPerformance}%`);
    console.log(`Rendering Time: ${metrics.renderingTime}ms`);
    
    if (metrics.memoryUsage) {
      console.log(`Memory Usage: ${metrics.memoryUsage.toFixed(2)}MB`);
    }

    // Performance warnings
    if (metrics.fps < this.config.targetFPS * 0.8) {
      console.warn('⚠️ Low FPS detected');
    }
    if (metrics.frameTime > 20) {
      console.warn('⚠️ High frame time detected');
    }
    if (metrics.scrollPerformance < 80) {
      console.warn('⚠️ Poor scroll performance detected');
    }

    console.groupEnd();
  }

  /**
   * Get current performance status
   */
  getPerformanceStatus(): 'excellent' | 'good' | 'fair' | 'poor' {
    const currentMetrics = this.calculateMetrics(performance.now());
    
    if (currentMetrics.fps >= this.config.targetFPS * 0.95 && 
        currentMetrics.scrollPerformance >= 90) {
      return 'excellent';
    } else if (currentMetrics.fps >= this.config.targetFPS * 0.8 && 
               currentMetrics.scrollPerformance >= 75) {
      return 'good';
    } else if (currentMetrics.fps >= this.config.targetFPS * 0.6 && 
               currentMetrics.scrollPerformance >= 60) {
      return 'fair';
    } else {
      return 'poor';
    }
  }

  /**
   * Optimize performance based on current metrics
   */
  optimizePerformance(): void {
    const status = this.getPerformanceStatus();
    
    switch (status) {
      case 'poor':
        // Reduce animation complexity
        document.documentElement.style.setProperty('--animation-duration', '0.1s');
        document.documentElement.style.setProperty('--transition-duration', '0.1s');
        break;
      case 'fair':
        // Moderate optimization
        document.documentElement.style.setProperty('--animation-duration', '0.2s');
        document.documentElement.style.setProperty('--transition-duration', '0.2s');
        break;
      default:
        // Restore normal animations
        document.documentElement.style.setProperty('--animation-duration', '0.3s');
        document.documentElement.style.setProperty('--transition-duration', '0.3s');
        break;
    }
  }
}

// Create singleton instance
const performanceMonitor = new PerformanceMonitor({
  enableLogging: process.env.NODE_ENV === 'development',
  enableMemoryTracking: true
});

export default performanceMonitor;
export type { PerformanceMetrics, PerformanceConfig };
