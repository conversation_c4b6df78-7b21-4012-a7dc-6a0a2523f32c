"use client"

import { But<PERSON> } from "@/components/ui/button"
import { motion, AnimatePresence } from "framer-motion"
import Image from 'next/image'
import Link from 'next/link'
import { useState, useMemo, useCallback, useEffect, useRef } from "react"
import { useIsMobile } from '@/app/utils/deviceDetection'
import TechStackSection from "./TechStackSection"
import NeuralNetwork from '../animations/NeuralNetwork'

// Move static data outside component to prevent recreation
const expertise = [
  {
    title: "Website Development with AI",
    description: "Full-stack development with modern AI technologies",
    icon: "💻"
  },
  {
    title: "WordPress & Shopify",
    description: "E-commerce and CMS solutions",
    icon: "🛍️"
  },
  {
    title: "UI/UX Design",
    description: "Figma & Framer expert",
    icon: "🎨"
  },
  {
    title: "SEO & Content",
    description: "Strategic content & optimization",
    icon: "📝"
  },
  {
    title: "AI Chat Bot Integration",
    description: "Advanced AI chatbot solutions",
    icon: "🤖",
    isNew: true
  },
  {
    title: "AI Business Integration",
    description: "Seamless AI business processes",
    icon: "🧠",
    isNew: true
  },
  {
    title: "AI Voice Assistant",
    description: "Ask AI voice-powered solutions",
    icon: "🎤",
    isNew: true
  },
  {
    title: "MOBILE APP Development (Ai Based App) ",
    description: "Cross-platform native mobile experiences",
    icon: "📱",
    isNew: true // Flag to indicate this is newly added
  }
]

const workProcess = [
  {
    step: "01",
    title: "Discovery & Planning",
    description: "Deep dive into requirements, tech stack selection, and project roadmap creation",
    icon: "🎯"
  },
  {
    step: "02",
    title: "Design & Architecture",
    description: "Creating scalable solutions with modern architecture patterns",
    icon: "⚡"
  },
  {
    step: "03",
    title: "Development & Testing",
    description: "Agile development with continuous integration and testing",
    icon: "🛠️"
  },
  {
    step: "04",
    title: "Launch & Support",
    description: "Smooth deployment and ongoing maintenance",
    icon: "🚀"
  }
]

const funFacts = [
  {
    icon: "🚀",
    title: "Code Wizard",
    fact: "I once debugged a production issue while sleeping! (In my dreams, of course)"
  },
  {
    icon: "🎮",
    title: "Gaming Dev",
    fact: "I hide easter eggs in all my projects. Can you find them all?"
  },
  {
    icon: "☕",
    title: "Coffee Powered",
    fact: "My code runs on a special fuel: Coffee.parseInt('espresso')"
  },
  {
    icon: "🌙",
    title: "Night Owl",
    fact: "Best code commits happen at 3 AM when the bugs are sleeping"
  },
  {
    icon: "🎵",
    title: "Code & Music",
    fact: "I listen to lofi beats while coding. It's my debugging soundtrack!"
  }
]

// AI-focused skills data with 4 slides
const aiSkills = [
  {
    category: "Enterprise AI Partnerships",
    skills: [
      { name: "OPENAI", level: 95, icon: "https://w7.pngwing.com/pngs/704/673/png-transparent-openai-chatgpt-logo.png", description: "GPT-4 & DALL-E integration specialist" },
      { name: "ANTHROPIC", level: 92, icon: "https://img.icons8.com/?size=100&id=H5H0mqCCr5AV&format=png&color=000000", description: "Claude 3 Opus implementation" },
      { name: "GOOGLE AI", level: 89, icon: "https://img.icons8.com/?size=100&id=rnK88i9FvAFO&format=png&color=000000", description: "Gemini Pro & PaLM API expert" },
      { name: "MISTRAL AI", level: 91, icon: "https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/mistral-color.png", description: "Mistral Large deployment" },
      { name: "GROK AI", level: 88, icon: "https://img.icons8.com/?size=100&id=USGXKHXKl9X7&format=png&color=000000", description: "Real-time market analysis" },
      { name: "DEEPSEEK", level: 90, icon: "https://img.icons8.com/?size=100&id=YWOidjGxCpFW&format=png&color=000000", description: "Code & chat implementation" },
      { name: "META AI", level: 87, icon: "https://img.icons8.com/?size=100&id=PvvcWRWxRKSR&format=png&color=000000", description: "Llama 2 & Code Llama expert" },
      { name: "PERPLEXITY", level: 89, icon: "https://img.icons8.com/?size=100&id=5WrDC03cg9ua&format=png&color=000000", description: "AI-powered search & research" },
      { name: "COHERE", level: 86, icon: "https://images.seeklogo.com/logo-png/51/2/cohere-logo-png_seeklogo-513871.png", description: "Command & Embed API specialist" },
      { name: "NVIDIA", level: 93, icon: "https://w7.pngwing.com/pngs/728/156/png-transparent-nvidia-flat-brand-logo-icon-thumbnail.png", description: "GPU acceleration & AI compute" }
    ]
  },
  {
    category: "LLM Expertise & Deployment",
    skills: [
      { name: "OLLAMA", level: 94, icon: "https://images.seeklogo.com/logo-png/59/2/ollama-logo-png_seeklogo-593420.png", description: "Local LLM deployment & optimization" },
      { name: "OPENROUTER", level: 92, icon: "https://images.seeklogo.com/logo-png/61/2/openrouter-logo-png_seeklogo-611674.png", description: "Multi-model routing & fallbacks" },
      { name: "HUGGING FACE", level: 90, icon: "https://w7.pngwing.com/pngs/839/288/png-transparent-hugging-face-favicon-logo-tech-companies-thumbnail.png", description: "Model fine-tuning & hosting" },
      { name: "REPLICATE", level: 88, icon: "https://logowik.com/content/uploads/images/replicate-ai23359.logowik.com.webp", description: "Cloud inference & model serving" },
      { name: "TOGETHER AI", level: 89, icon: "https://images.seeklogo.com/logo-png/61/2/together-ai-logo-png_seeklogo-611707.png", description: "Enterprise LLM infrastructure" },
      { name: "PERPLEXITY AI", level: 87, icon: "https://images.seeklogo.com/logo-png/61/1/perplexity-ai-icon-black-logo-png_seeklogo-611679.png", description: "Search-augmented generation" }
    ]
  },
  {
    category: "AI Integration Technologies",
    skills: [
      { name: "LANGCHAIN", level: 94, icon: "https://images.seeklogo.com/logo-png/61/1/langchain-icon-logo-png_seeklogo-611655.png", description: "Advanced agent frameworks" },
      { name: "LLAMAINDEX", level: 91, icon: "https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/llamaindex-color.png", description: "RAG & knowledge retrieval" },
      { name: "VECTOR DATABASES", level: 93, icon: "https://icons.veryicon.com/png/o/miscellaneous/streamline/database-39.png", description: "Pinecone, Chroma, Weaviate" },
      { name: "EMBEDDINGS", level: 89, icon: "https://static.thenounproject.com/png/3147500-200.png", description: "Text & image embedding systems" },
      { name: "LANGSMITH", level: 88, icon: "https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/langsmith-color.png", description: "LLM app observability platform" },
      { name: "FLOWISE", level: 86, icon: "https://docs.flowiseai.com/~gitbook/image?url=https%3A%2F%2F4068692976-files.gitbook.io%2F%7E%2Ffiles%2Fv0%2Fb%2Fgitbook-x-prod.appspot.com%2Fo%2Fspaces%252FUiD7nOmFRK805sNuiieJ%252Fuploads%252FXxve37yNH63uTNoBYJ1Y%252FFlowise%2520Cropped%2520White%2520High%2520Res.png%3Falt%3Dmedia%26token%3De022b6af-b237-4724-9e4c-7b895cc3cdbe&width=768&dpr=4&quality=100&sign=8f7e6694&sv=2", description: "Visual LLM workflow builder" },
      { name: "SEMANTIC KERNEL", level: 85, icon: "https://devblogs.microsoft.com/semantic-kernel/wp-content/uploads/sites/78/2024/03/Large_SK_Logo.png", description: "Microsoft's AI orchestration" }
    ]
  },
  {
    category: "AI Training & Optimization",
    skills: [
      { name: "MODEL FINE-TUNING", level: 90, icon: "⚙️", description: "Custom model adaptation" },
      { name: "PROMPT ENGINEERING", level: 93, icon: "✨", description: "Advanced prompt techniques" },
      { name: "INFERENCE OPTIMIZATION", level: 91, icon: "⚡", description: "Latency & throughput tuning" },
      { name: "QUANTIZATION", level: 87, icon: "📉", description: "Model size & performance balancing" }
    ]
  },
  {
    category: "Enterprise AI Solutions",
    skills: [
      { name: "MULTI-AGENT SYSTEMS", level: 92, icon: "👥", description: "Collaborative AI workflows" },
      { name: "CUSTOM AI WORKFLOWS", level: 89, icon: "🔧", description: "Business process integration" },
      { name: "REAL-TIME PROCESSING", level: 91, icon: "⏱️", description: "Streaming & event processing" },
      { name: "AI SECURITY & PRIVACY", level: 94, icon: "🔐", description: "Enterprise-grade safeguards" }
    ]
  }
]

// Pre-define animation variants with optimized values
const fadeInUpVariant = {
  hidden: { opacity: 0, y: 10 },
  visible: { opacity: 1, y: 0 }
}

const scaleInVariant = {
  hidden: { opacity: 0, scale: 0.98 },
  visible: { opacity: 1, scale: 1 }
}

// Define sparkle animation with reduced complexity
const sparkleVariants = {
  animate: {
    scale: [1, 1.1, 1],
    opacity: [0.6, 1, 0.6],
    transition: {
      duration: 2.5,
      repeat: Infinity,
      repeatType: "loop" as const,
      ease: "linear"
    }
  }
}

// Optimize bounce animation
const bounceVariants = {
  animate: {
    y: [0, -3, 0],
    transition: {
      duration: 2,
      repeat: Infinity,
      repeatType: "loop" as const,
      ease: "linear"
    }
  }
}

// Add optimized skill card animation
const skillCardVariants = {
  hidden: { opacity: 0, x: 10 },
  visible: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: -10 }
}

export default function Hero() {
  const [showSecretPanel, setShowSecretPanel] = useState(false)
  const isMobile = useIsMobile()
  const [funFactIndex, setFunFactIndex] = useState(0)
  const [activeSkillSet, setActiveSkillSet] = useState(0)
  const [isAutoAnimating, setIsAutoAnimating] = useState(true)
  const containerRef = useRef<HTMLDivElement>(null)

  // Memoize handlers
  const nextFunFact = useCallback(() => {
    setFunFactIndex((prev) => (prev + 1) % funFacts.length)
  }, [])

  const toggleSecretPanel = useCallback(() => {
    setShowSecretPanel(prev => !prev)
  }, [])

  // Add carousel navigation
  const nextSkillSet = useCallback(() => {
    setActiveSkillSet((prev) => (prev + 1) % aiSkills.length)
  }, [])

  const prevSkillSet = useCallback(() => {
    setActiveSkillSet((prev) => (prev - 1 + aiSkills.length) % aiSkills.length)
  }, [])

  // Add auto-animation toggle handler
  const toggleAutoAnimation = useCallback(() => {
    setIsAutoAnimating(prev => !prev)
  }, [])

  // Add another callback to manually navigate in vertical direction
  const nextVerticalSkillSet = useCallback(() => {
    setActiveSkillSet((prev) => (prev + 1) % aiSkills.length)
  }, [])

  const prevVerticalSkillSet = useCallback(() => {
    setActiveSkillSet((prev) => (prev - 1 + aiSkills.length) % aiSkills.length)
  }, [])

  // Auto advance carousel only when auto-animation is enabled
  useEffect(() => {
    // Don't set up any interval if auto-animation is disabled
    if (!isAutoAnimating) return;
    
    let intervalId: NodeJS.Timeout;
    
    const startAutoAdvance = () => {
      // Clear any existing interval first
      if (intervalId) clearInterval(intervalId);
      
      // Set up new interval
      intervalId = setInterval(() => {
        // Only advance if document is visible and auto-animation is still enabled
        if (!document.hidden && isAutoAnimating) {
          nextSkillSet();
        }
      }, 8000); // 8 seconds interval
    };
    
    // Start auto-advance
    startAutoAdvance();
    
    // Handle visibility changes
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Clear interval when tab is hidden
        clearInterval(intervalId);
      } else if (isAutoAnimating) {
        // Restart interval when tab becomes visible again
        startAutoAdvance();
      }
    };
    
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // Clean up function
    return () => {
      if (intervalId) clearInterval(intervalId);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [nextSkillSet, isAutoAnimating]);

  // Memoize current fun fact
  const currentFunFact = useMemo(() => funFacts[funFactIndex], [funFactIndex])
  // Memoize current skill set
  const currentSkillSet = useMemo(() => aiSkills[activeSkillSet], [activeSkillSet])

  const variants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  const mobileVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 }
  }

  // Optimize scroll performance with useCallback
  const handleScroll = useCallback(() => {
    // Use requestAnimationFrame for smooth scrolling
    requestAnimationFrame(() => {
      if (containerRef.current) {
        const scrollY = window.scrollY;
        containerRef.current.style.transform = `translate3d(0, ${scrollY * 0.1}px, 0)`;
      }
    });
  }, []);

  // Add scroll listener with cleanup
  useEffect(() => {
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  return (
    <motion.section
      ref={containerRef}
      initial="hidden"
      animate="visible"
      variants={isMobile ? mobileVariants : variants}
      transition={{
        duration: isMobile ? 0.2 : 0.3,
        ease: [0.22, 1, 0.36, 1] // Custom cubic-bezier for 60fps smoothness
      }}
      className="relative min-h-screen flex flex-col justify-center items-center py-20 px-6 mt-16 sm:mt-20 bg-black will-change-transform"
      style={{
        transform: 'translate3d(0, 0, 0)',
        backfaceVisibility: 'hidden',
        perspective: '1000px',
        containIntrinsicSize: '0 100vh',  // Add size containment
        contentVisibility: 'auto',        // Enable content-visibility optimization
        contain: 'paint layout style',    // Enhanced containment for 60fps
        willChange: 'transform, opacity', // Explicit will-change for hardware acceleration
      }}
    >
      {/* Neural Network Background - Top, Left, Right Areas */}
      <div className="absolute inset-0 z-[1] pointer-events-none overflow-hidden">
        <NeuralNetwork
          color="#a855f7"
          lineColor="#8b5cf6"
          pointCount={60} // More points for better network coverage
          connectionRadius={250} // Longer connections for visible network
          speed={0.5} // Smooth movement
          containerBounds={true} // Contained within hero section
        />
      </div>

      {/* Optimize background layers with hardware acceleration */}
      <div className="fixed inset-0 bg-[#050509]/85 z-[-2] will-change-transform"
           style={{ transform: 'translate3d(0, 0, 0)' }}></div>
      <div className="absolute inset-0 bg-gradient-to-b from-[#050509]/60 via-[#07051a]/60 to-[#050509]/60 opacity-70 z-[-1] will-change-transform"
           style={{ transform: 'translate3d(0, 0, 0)' }}></div>
      
      {/* Optimize gradient effects */}
      <div className="absolute inset-0 overflow-hidden z-[-1]"
           style={{ 
             transform: 'translate3d(0, 0, 0)',
             contain: 'paint layout style'
           }}>
        {/* Optimize vignette effect */}
        <div className="absolute inset-0 z-0"
             style={{ 
               background: 'radial-gradient(circle, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.85) 100%)',
               transform: 'translate3d(0, 0, 0)',
               backfaceVisibility: 'hidden',
               contain: 'paint layout'
             }}></div>
        
        {/* Optimize gradient blobs with hardware acceleration */}
        <div className="absolute top-0 right-1/4 w-[500px] h-[500px] bg-purple-900/15 rounded-full blur-[40px]"
             style={{ transform: 'translate3d(0, 0, 0)', contain: 'paint layout' }}></div>
        <div className="absolute bottom-0 left-1/4 w-[400px] h-[400px] bg-indigo-900/15 rounded-full blur-[40px]"
             style={{ transform: 'translate3d(0, 0, 0)', contain: 'paint layout' }}></div>
        <div className="absolute top-1/2 right-1/2 translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-violet-900/10 rounded-full blur-[40px]"
             style={{ transform: 'translate3d(0, 0, 0)', contain: 'paint layout' }}></div>
             
        {/* Optimize gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-tr from-black/70 via-purple-950/5 to-black/70 opacity-80"
             style={{ transform: 'translate3d(0, 0, 0)', contain: 'paint layout' }}></div>
      </div>

      {/* Main content - Optimize with containment */}
      <div className="max-w-7xl mx-auto px-6 sm:px-8 grid grid-cols-1 lg:grid-cols-2 gap-10 sm:gap-16 relative z-[1] overflow-x-hidden"
           style={{
             transform: 'translate3d(0, 0, 0)',
             contain: 'paint layout style',
             willChange: 'transform'
           }}>
        {/* Left Column - Add containment */}
        <div className="space-y-8 sm:space-y-10 mt-8 md:mt-0 px-2 sm:px-4"
             style={{ contain: 'paint layout style' }}>
          <motion.div 
            variants={fadeInUpVariant}
            initial="hidden"
            animate="visible"
            transition={{
              duration: 0.2,
              ease: [0.22, 1, 0.36, 1] // 60fps optimized easing
            }}
            className="space-y-4 sm:space-y-6"
          >
            <div className="text-sm bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 text-transparent bg-clip-text">
              hello I am
            </div>

            <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold leading-tight">
              <motion.div 
                className="text-white cursor-pointer relative group inline-block"
                onClick={toggleSecretPanel}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                transition={{ duration: 0.2 }}
              >
                {/* Fun Fact Indicator - Visible on all devices */}
                <div className="absolute -top-8 left-[75%] transform -translate-x-1/2 text-sm text-purple-400 whitespace-nowrap
                              flex items-center gap-2 animate-bounce">
                  <span className="text-base rotate-[180deg]">👆</span>
                  <span className="text-xs font-medium bg-black/80 px-2 py-1 rounded-full border border-purple-500/30 backdrop-blur-sm">
                    Click for fun fact!
                  </span>
                </div>
                
                ALI <span className="bg-white text-black px-2 rounded-lg group-hover:bg-purple-500 group-hover:text-white transition-all duration-300">HASNAAT</span>
              </motion.div>
              <div className="block mt-6 md:mt-4">
                <span className="border-2 border-white text-white px-4 sm:px-6 py-2 rounded-lg text-sm sm:text-base tracking-widest hover:bg-white hover:text-black transition-all duration-300 cursor-default inline-block">
                  FULLSTACK & AI SYSTEMS DEVELOPER
                </span>
                <motion.div 
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2 }}
                  className="relative inline-flex items-center ml-2 group"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-violet-500/20 rounded-full blur-sm transform group-hover:scale-110 transition-transform duration-300"></div>
                  <span className="relative px-2 py-0.5 text-[10px] font-medium bg-black/50 text-purple-300 rounded-full border border-purple-500/30 backdrop-blur-sm
                                 group-hover:text-purple-200 group-hover:border-purple-500/50 transition-all duration-300 tracking-wider flex items-center gap-1">
                    <span className="animate-pulse">✨</span> Ai-Is OUR TOP PRIORITY
                  </span>
                </motion.div>
              </div>
            </h1>

            {/* Mobile Easter Egg Box */}
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="sm:hidden mt-4 relative"
            >
              <button
                onClick={toggleSecretPanel}
                className="w-full px-4 py-3 rounded-xl border border-purple-500/30 
                         bg-gradient-to-r from-purple-500/10 to-purple-600/10
                         hover:from-purple-500/20 hover:to-purple-600/20 
                         backdrop-blur-sm transition-all duration-300
                         flex items-center justify-between group"
              >
                <div className="flex items-center gap-3">
                  <span className="text-xl animate-pulse">✨</span>
                  <div className="text-left">
                    <p className="text-white/90 text-sm font-medium">Discover Fun Facts</p>
                    <p className="text-purple-400/80 text-xs">Tap to reveal secrets!</p>
                  </div>
                </div>
                <span className="text-purple-400/80 text-lg group-hover:translate-x-1 transition-transform duration-300">→</span>
              </button>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{
                delay: 0.05,
                duration: 0.15,
                ease: [0.22, 1, 0.36, 1]
              }}
              className="space-y-8 md:space-y-6 mt-6 md:mt-2 px-2 py-4"
              style={{
                willChange: 'transform, opacity',
                transform: 'translate3d(0, 0, 0)'
              }}
              suppressHydrationWarning
            >
              <div className="space-y-6">
                <div className="text-base sm:text-lg text-gray-400 max-w-xl leading-relaxed">
                  Crafting exceptional digital experiences through clean code, innovative solutions, and intelligent AI integration — leveraging tools like LangChain, GPT, and NLP to build smarter, scalable systems.
                </div>
                <div className="flex flex-wrap gap-3 items-center text-sm text-gray-400">
                  <span className="flex items-center gap-2 px-3 py-2 bg-black/30 rounded-lg border border-white/10">
                    <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                    Available for new projects
                  </span>
                  <span className="hidden sm:inline px-2">•</span>
                  <span className="px-3 py-2 bg-black/30 rounded-lg border border-white/10">Based in Pakistan</span>
                </div>
              </div>
            </motion.div>
          </motion.div>

          {/* Work Process - Mobile Optimized */}
          <div className="space-y-2 sm:space-y-6 mt-6 sm:mt-12">
            <h3 className="text-lg sm:text-xl font-semibold text-white/90 px-1 mb-2 sm:mb-4">How I Work</h3>
            <div className="grid grid-cols-1 gap-2 sm:gap-4">
              {workProcess.map((process, index) => (
                <motion.div
                  key={process.step}
                  variants={fadeInUpVariant}
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true }}
                  transition={{ 
                    duration: 0.2, 
                    delay: index * 0.1,
                    ease: [0.22, 1, 0.36, 1]
                  }}
                  className="flex items-start gap-2 sm:gap-3 p-2.5 sm:p-4 rounded-xl border border-white/10 
                           hover:border-purple-500/50 transition-all bg-white/5 hover:bg-white/10"
                >
                  <div className="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 rounded-lg bg-purple-500/10 
                               border border-purple-500/20 shrink-0">
                    <span className="text-base sm:text-xl">{process.icon}</span>
                  </div>
                  <div className="space-y-0.5 sm:space-y-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <span className="text-xs font-medium text-purple-400/80">{process.step}</span>
                      <h4 className="font-medium text-sm sm:text-base text-white/90">{process.title}</h4>
                    </div>
                    <p className="text-xs sm:text-sm text-gray-400 leading-relaxed">{process.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          <div className="flex flex-wrap gap-3 sm:gap-4 mt-6 sm:mt-8">
            <Link 
              href="/contact" 
              className="bg-white text-black px-4 sm:px-6 py-2 sm:py-3 rounded-lg font-medium hover:bg-gray-100 transition group flex items-center gap-2 text-sm sm:text-base"
            >
              Let's Talk 
              <motion.span 
                className="inline-block"
                animate={{ x: [0, 5, 0] }}
                transition={{ repeat: Infinity, duration: 1.5 }}
              >
                →
              </motion.span>
            </Link>
            <Link 
              href="/projects" 
              className="border-2 border-white text-white px-4 sm:px-6 py-2 sm:py-3 rounded-lg font-medium hover:bg-white hover:text-black transition flex items-center gap-2 text-sm sm:text-base whitespace-nowrap"
            >
              View Projects
              <motion.span 
                className="inline-block"
                animate={{ x: [0, 5, 0] }}
                transition={{ repeat: Infinity, duration: 1.5, delay: 0.2 }}
              >
                →
              </motion.span>
            </Link>
          </div>
        </div>

        {/* Right Column - Mobile Optimized */}
        <motion.div 
          variants={isMobile ? mobileVariants : scaleInVariant}
          initial="hidden"
          animate="visible"
          transition={{ duration: isMobile ? 0.3 : 0.5 }}
          className="flex flex-col gap-4 sm:gap-6"
        >
          {/* Replace the Text Showcase Section with Tech Skills Carousel */}
          <motion.div 
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.2 }}
            className="text-center space-y-4 pt-2"
          >
            {/* Optimized skills showcase with pure frosted glass effect */}
            <div className="relative h-[230px] sm:h-[270px] w-[90%] mx-auto group overflow-hidden rounded-xl sm:rounded-2xl"
                 style={{
                   transform: 'translate3d(0, 0, 0)',
                   backfaceVisibility: 'hidden',
                   willChange: 'transform',
                   contain: 'layout paint style'
                 }}>
              {/* Pure frosted glass effect - optimized layers */}
              <div className="absolute inset-0 backdrop-blur-2xl bg-transparent"></div>
              <div className="absolute inset-0 backdrop-blur-xl bg-black/5"></div>
              
              {/* Subtle gradient for depth */}
              <div className="absolute inset-0 bg-gradient-to-b from-black/5 via-transparent to-transparent pointer-events-none"></div>
              
              {/* Clean neon border without glow */}
              <div className="absolute inset-0 rounded-xl sm:rounded-2xl border-2 border-purple-500/50"></div>
              
              {/* Subtle neon accent line */}
              <div className="absolute top-0 left-[5%] right-[5%] h-[1px] bg-gradient-to-r from-transparent via-purple-400/70 to-transparent"></div>
              <div className="absolute bottom-0 left-[5%] right-[5%] h-[1px] bg-gradient-to-r from-transparent via-purple-400/70 to-transparent"></div>
              
              <div className="h-full w-full p-3 sm:p-5 relative z-10">
                <div className="h-full flex flex-col justify-between">
                  <div className="flex items-center justify-between mb-2 sm:mb-4">
                    <h3 className="text-sm sm:text-base font-medium flex items-center gap-1.5 sm:gap-2">
                      <span className="text-white/90">{currentSkillSet.category}</span>
                      <motion.span 
                        animate={{ rotate: [0, 5, 0, -5, 0] }}
                        transition={{ 
                          duration: 1.5, 
                          repeat: Infinity, 
                          repeatDelay: 3 
                        }}
                        className="text-white/70"
                      >
                        {currentSkillSet.category === "Enterprise AI Partnerships" ? "🤝" : 
                         currentSkillSet.category === "LLM Expertise & Deployment" ? "🦙" :
                         currentSkillSet.category === "AI Integration Technologies" ? "⛓️" :
                         currentSkillSet.category === "AI Training & Optimization" ? "⚙️" : "👥"}
                      </motion.span>
                    </h3>
                    <div className="flex items-center gap-2 sm:gap-3">
                      {/* Enhanced auto/manual toggle with neon effect */}
                      <div className="flex items-center gap-1.5">
                        <span className="text-[10px] text-white/80 font-medium">{isAutoAnimating ? "Auto" : "Manual"}</span>
                        <motion.button
                          onClick={toggleAutoAnimation}
                          className="relative h-6 rounded-full transition-all duration-300 overflow-hidden flex items-center px-1 border"
                          style={{
                            background: isAutoAnimating ? 'rgba(139, 92, 246, 0.15)' : 'rgba(255, 255, 255, 0.05)',
                            width: '36px',
                            borderColor: isAutoAnimating ? 'rgba(139, 92, 246, 0.5)' : 'rgba(255, 255, 255, 0.2)',
                            boxShadow: isAutoAnimating ? '0 0 5px rgba(139, 92, 246, 0.5)' : 'none'
                          }}
                        >
                          <motion.div
                            animate={{
                              x: isAutoAnimating ? 18 : 0,
                              backgroundColor: isAutoAnimating ? 'rgba(139, 92, 246, 0.9)' : 'rgba(255, 255, 255, 0.5)',
                              boxShadow: isAutoAnimating ? '0 0 5px rgba(139, 92, 246, 0.8)' : 'none'
                            }}
                            transition={{ duration: 0.3 }}
                            className="absolute w-4 h-4 rounded-full"
                          />
                          <span className="sr-only">{isAutoAnimating ? 'Auto mode' : 'Manual mode'}</span>
                        </motion.button>
                      </div>

                      {/* Compact navigation controls */}
                      <div className="flex gap-1 sm:gap-1.5">
                        <motion.button 
                          onClick={prevSkillSet}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          className="p-1 sm:p-1.5 rounded-lg bg-white/5 hover:bg-white/10
                                   border border-white/10 hover:border-purple-500/30
                                   transition-all duration-300 group"
                          aria-label="Previous skill set"
                        >
                          <span className="text-xs sm:text-sm text-white/70 group-hover:text-white/90 transition-colors">←</span>
                        </motion.button>
                        <motion.button 
                          onClick={nextSkillSet}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          className="p-1 sm:p-1.5 rounded-lg bg-white/5 hover:bg-white/10
                                   border border-white/10 hover:border-purple-500/30
                                   transition-all duration-300 group"
                          aria-label="Next skill set"
                        >
                          <span className="text-xs sm:text-sm text-white/70 group-hover:text-white/90 transition-colors">→</span>
                        </motion.button>
                      </div>
                    </div>
                  </div>

                  <div className="flex-1 overflow-hidden">
                    <AnimatePresence mode="wait">
                      <motion.div 
                        key={activeSkillSet}
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -20 }}
                        transition={{ duration: 0.3 }}
                        className="h-full"
                      >
                        <div className="grid auto-rows-min gap-1.5 h-full overflow-y-auto scrollbar-thin scrollbar-thumb-purple-500/20 scrollbar-track-transparent pr-1 py-0.5">
                          {currentSkillSet.skills.map((skill, idx) => (
                            <motion.div
                              key={skill.name}
                              initial={{ opacity: 0, x: 10 }}
                              animate={{ 
                                opacity: 1, 
                                x: 0,
                                transition: { 
                                  duration: 0.15,
                                  delay: idx * 0.03
                                }
                              }}
                              className="flex items-center gap-2 transition-all group/skill hover:bg-white/[0.03] p-1.5 rounded-lg h-[42px]"
                              style={{
                                willChange: 'transform, opacity',
                                transform: 'translate3d(0, 0, 0)'
                              }}
                            >
                              <div className={`${skill.icon.startsWith('http') ? 'w-10 h-10 bg-white' : 'w-10 h-10 bg-black/10'} flex items-center justify-center rounded-md
                                           border border-purple-500/30
                                           group-hover/skill:border-purple-500/50
                                           transition-colors duration-200 p-1`}>
                                {skill.icon.startsWith('http') ? (
                                  <img
                                    src={skill.icon}
                                    alt={skill.name}
                                    className="w-8 h-8 object-contain group-hover/skill:scale-110 transition-transform duration-300"
                                    style={{ filter: 'brightness(1.1) contrast(1.3) saturate(1.1)' }}
                                  />
                                ) : (
                                  <span className="text-lg group-hover/skill:scale-110 transition-transform duration-300">{skill.icon}</span>
                                )}
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center justify-between mb-1">
                                  <div className="flex items-center gap-2">
                                    <span className="text-white/90 text-xs font-medium group-hover/skill:text-white/100 transition-colors">{skill.name}</span>
                                    <span className="text-[10px] text-white/80 font-medium tabular-nums bg-black/20 px-1.5 py-0.5 rounded-full border border-purple-500/30">{skill.level}%</span>
                                  </div>
                                </div>
                                <div className="flex items-center gap-1.5">
                                  <div className="h-1 flex-1 bg-black/20 rounded-full overflow-hidden">
                                    <motion.div 
                                      className="h-full rounded-full relative"
                                      style={{
                                        background: "linear-gradient(90deg, rgba(139,92,246,0.6) 0%, rgba(168,85,247,0.7) 100%)",
                                        willChange: 'width',
                                        transform: 'translate3d(0, 0, 0)'
                                      }}
                                      initial={{ width: 0 }}
                                      animate={{ 
                                        width: `${skill.level}%`,
                                        transition: { 
                                          duration: 0.4,
                                          delay: idx * 0.02,
                                          ease: "easeOut"
                                        }
                                      }}
                                    >
                                      <div className="absolute right-0 top-0 h-full w-[1px] bg-purple-300/80"></div>
                                    </motion.div>
                                  </div>
                                  <motion.div 
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    transition={{ delay: 0.3 + idx * 0.05 }}
                                    className="text-[8px] text-white/50 hidden sm:block"
                                  >
                                    {skill.description && (
                                      <span className="whitespace-nowrap text-[8px] sm:text-[9px] text-white/50 truncate max-w-[100px] sm:max-w-[140px] block">{skill.description}</span>
                                    )}
                                  </motion.div>
                                </div>
                              </div>
                            </motion.div>
                          ))}
                        </div>
                      </motion.div>
                    </AnimatePresence>
                  </div>

                  {/* Optimized carousel indicators */}
                  <div className="flex justify-center gap-1.5 pt-2">
                    {aiSkills.map((skillSet, idx: number) => (
                      <button
                        key={idx}
                        onClick={() => setActiveSkillSet(idx)}
                        className="group flex flex-col items-center"
                        aria-label={`Go to ${skillSet.category} skills`}
                      >
                        <div 
                          className={`h-0.5 rounded-full transition-all duration-200 ${
                            idx === activeSkillSet 
                              ? 'bg-purple-500 w-5' 
                              : 'bg-white/10 group-hover:bg-white/20 w-2.5'
                          }`}
                        />
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Stats Grid - Mobile Optimized */}
          <div className="grid grid-cols-2 gap-6 sm:gap-8 px-2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{
                delay: 0.1,
                duration: 0.15,
                ease: [0.22, 1, 0.36, 1]
              }}
              className="border border-white/20 p-5 sm:p-6 rounded-xl hover:border-white transition-colors bg-black/30 backdrop-blur-sm shadow-lg"
              style={{
                willChange: 'transform, opacity',
                transform: 'translate3d(0, 0, 0)'
              }}
            >
              <h4 className="text-2xl sm:text-3xl font-bold text-white mb-2">50+</h4>
              <p className="text-xs sm:text-sm text-gray-400">Projects Completed</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{
                delay: 0.15,
                duration: 0.15,
                ease: [0.22, 1, 0.36, 1]
              }}
              className="border border-white/20 p-5 sm:p-6 rounded-xl hover:border-white transition-colors bg-black/30 backdrop-blur-sm shadow-lg"
              style={{
                willChange: 'transform, opacity',
                transform: 'translate3d(0, 0, 0)'
              }}
            >
              <h4 className="text-2xl sm:text-3xl font-bold text-white mb-2">4+</h4>
              <p className="text-xs sm:text-sm text-gray-400">Years Experience</p>
            </motion.div>
          </div>

          {/* Skills Section - Bento Layout Optimized */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
              delay: 0.2,
              duration: 0.15,
              ease: [0.22, 1, 0.36, 1]
            }}
            className="space-y-3"
            style={{
              willChange: 'transform, opacity',
              transform: 'translate3d(0, 0, 0)'
            }}
          >
            <h4 className="text-lg font-semibold text-white mb-3 text-center">Skills & Expertise</h4>
            <div className="grid grid-cols-2 lg:grid-cols-3 gap-3 auto-rows-fr">
              {expertise.map((skill, index) => (
                <motion.div
                  key={skill.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{
                    delay: 0.25 + index * 0.03,
                    duration: 0.15,
                    ease: [0.22, 1, 0.36, 1]
                  }}
                  style={{
                    willChange: 'transform, opacity',
                    transform: 'translate3d(0, 0, 0)'
                  }}
                  className="relative p-4 rounded-lg backdrop-blur-md bg-white/5 border border-white/10 hover:bg-white/10 hover:border-white/20 transition-all duration-300 group min-h-[100px] flex items-center justify-center"
                >
                  {/* Compact badge for new items */}
                  {skill.isNew && (
                    <div className="absolute -top-1 -right-1 z-10">
                      <span className="backdrop-blur-md bg-white/20 border border-white/30 text-white text-[8px] font-medium px-1 py-0.5 rounded-full shadow-lg">
                        NEW
                      </span>
                    </div>
                  )}

                  <div className="flex flex-col items-center text-center space-y-2 w-full">
                    <span className="text-2xl group-hover:scale-110 transition-transform duration-300">{skill.icon}</span>
                    <div className="space-y-1">
                      <h5 className="text-white text-sm font-semibold leading-tight">{skill.title}</h5>
                      <p className="text-gray-400 text-xs leading-tight opacity-80">{skill.description}</p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Tech Stack Section */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.7 }}
        className="w-full mt-12 sm:mt-16 max-w-7xl mx-auto relative"
      >
        <TechStackSection />
      </motion.div>

      {/* Fun Facts Modal - Mobile Optimized */}
      <AnimatePresence>
        {showSecretPanel && (
          <motion.div
            initial={{ opacity: 0, y: "100%" }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: "100%" }}
            transition={{ type: "spring", damping: 25, stiffness: 200 }}
            className="fixed bottom-0 left-0 right-0 z-50 sm:fixed sm:top-1/2 sm:left-1/2 
                      sm:transform sm:-translate-x-1/2 sm:-translate-y-1/2
                      bg-black/95 sm:rounded-xl border-t border-purple-500/50 sm:border
                      backdrop-blur-lg w-full sm:w-[90%] sm:max-w-md mx-auto 
                      shadow-2xl shadow-purple-500/20"
          >
            <div className="relative p-6">
              <div className="absolute right-4 top-4 sm:-top-3 sm:-right-3">
                <button 
                  onClick={toggleSecretPanel}
                  className="w-8 h-8 bg-black/80 rounded-full flex items-center justify-center 
                            text-white hover:bg-purple-500/40 transition-colors 
                            border-2 border-purple-500/50 shadow-lg text-lg"
                >
                  ×
                </button>
              </div>
              
              <motion.div
                key={funFactIndex}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.2 }}
                className="text-center space-y-4 pt-2"
              >
                <div className="bg-gradient-to-r from-purple-500/20 via-purple-400/20 to-purple-500/20 
                              p-5 rounded-lg border border-purple-500/30">
                  <span className="text-4xl sm:text-5xl block mb-2">{currentFunFact.icon}</span>
                  <h3 className="text-lg sm:text-xl font-bold bg-gradient-to-r from-purple-400 to-purple-200 bg-clip-text text-transparent">
                    {currentFunFact.title}
                  </h3>
                </div>
                <p className="text-sm sm:text-base text-gray-300 px-2 leading-relaxed">
                  {currentFunFact.fact}
                </p>
                <button
                  onClick={nextFunFact}
                  className="w-full px-4 py-3 bg-gradient-to-r from-purple-500/20 to-purple-600/20 
                           rounded-lg hover:from-purple-500/30 hover:to-purple-600/30 
                           transition-all duration-300 text-white text-sm font-medium 
                           border-2 border-purple-500/30 hover:border-purple-500/50 
                           active:scale-95 shadow-lg shadow-purple-500/20
                           flex items-center justify-center gap-2"
                >
                  <span>Next Fun Fact</span>
                  <span className="text-lg">→</span>
                </button>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.section>
  )
} 
