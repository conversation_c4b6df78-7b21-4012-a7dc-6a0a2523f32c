{"dbPath": "D:\\MY DETAILED PORTFOLIO\\portflio\\projects.db", "fileSize": 12288, "tables": [{"name": "projects", "columns": [{"name": "id", "type": "INTEGER", "notNull": false, "defaultValue": null, "primaryKey": true}, {"name": "title", "type": "TEXT", "notNull": true, "defaultValue": null, "primaryKey": false}, {"name": "description", "type": "TEXT", "notNull": true, "defaultValue": null, "primaryKey": false}, {"name": "image", "type": "TEXT", "notNull": true, "defaultValue": null, "primaryKey": false}, {"name": "secondImage", "type": "TEXT", "notNull": false, "defaultValue": null, "primaryKey": false}, {"name": "showBothImagesInPriority", "type": "INTEGER", "notNull": false, "defaultValue": "0", "primaryKey": false}, {"name": "category", "type": "TEXT", "notNull": true, "defaultValue": null, "primaryKey": false}, {"name": "technologies", "type": "TEXT", "notNull": false, "defaultValue": "'[]'", "primaryKey": false}, {"name": "link", "type": "TEXT", "notNull": true, "defaultValue": null, "primaryKey": false}, {"name": "features", "type": "TEXT", "notNull": false, "defaultValue": "'[]'", "primaryKey": false}, {"name": "exclusiveFeatures", "type": "TEXT", "notNull": false, "defaultValue": "'[]'", "primaryKey": false}, {"name": "featured", "type": "INTEGER", "notNull": false, "defaultValue": "0", "primaryKey": false}, {"name": "visualEffects", "type": "TEXT", "notNull": false, "defaultValue": "'{\"animation\":\"none\",\"showBadge\":false}'", "primaryKey": false}, {"name": "imagePriority", "type": "INTEGER", "notNull": false, "defaultValue": "5", "primaryKey": false}, {"name": "status", "type": "TEXT", "notNull": false, "defaultValue": null, "primaryKey": false}, {"name": "updatedDays", "type": "INTEGER", "notNull": false, "defaultValue": null, "primaryKey": false}, {"name": "progress", "type": "INTEGER", "notNull": false, "defaultValue": null, "primaryKey": false}, {"name": "developmentProgress", "type": "INTEGER", "notNull": false, "defaultValue": null, "primaryKey": false}, {"name": "estimatedCompletion", "type": "TEXT", "notNull": false, "defaultValue": null, "primaryKey": false}, {"name": "lastUpdated", "type": "TEXT", "notNull": false, "defaultValue": null, "primaryKey": false}], "rowCount": 0, "sampleRow": null}, {"name": "sqlite_sequence", "columns": [{"name": "name", "type": "", "notNull": false, "defaultValue": null, "primaryKey": false}, {"name": "seq", "type": "", "notNull": false, "defaultValue": null, "primaryKey": false}], "rowCount": 0, "sampleRow": null}], "error": null, "tableNames": ["projects", "sqlite_sequence"]}