'use client';

import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { cn } from '@/lib/utils';

type AnimationType = 'fade' | 'slide-up' | 'slide-down' | 'slide-left' | 'slide-right' | 'scale' | 'none';

export interface SmoothAnimationProps {
  children: React.ReactNode;
  type?: AnimationType;
  className?: string;
  duration?: number;
  delay?: number;
  repeat?: number;
  shouldAnimate?: boolean;
  forceAnimation?: boolean; // Force animation even if reduced motion is preferred
  onComplete?: () => void;
  trigger?: 'mount' | 'hover' | 'manual';
}

// GSAP animation configurations
const getAnimationConfig = (type: AnimationType) => {
  switch (type) {
    case 'fade':
      return {
        from: { opacity: 0 },
        to: { opacity: 1 }
      };
    case 'slide-up':
      return {
        from: { opacity: 0, y: 50 },
        to: { opacity: 1, y: 0 }
      };
    case 'slide-down':
      return {
        from: { opacity: 0, y: -50 },
        to: { opacity: 1, y: 0 }
      };
    case 'slide-left':
      return {
        from: { opacity: 0, x: 50 },
        to: { opacity: 1, x: 0 }
      };
    case 'slide-right':
      return {
        from: { opacity: 0, x: -50 },
        to: { opacity: 1, x: 0 }
      };
    case 'scale':
      return {
        from: { opacity: 0, scale: 0.9 },
        to: { opacity: 1, scale: 1 }
      };
    default:
      return {
        from: {},
        to: {}
      };
  }
};

export function SmoothAnimation({
  children,
  type = 'fade',
  className = '',
  duration = 0.5,
  delay = 0,
  repeat = 0,
  shouldAnimate = true,
  forceAnimation = false,
  onComplete,
  trigger = 'mount',
  ...props
}: SmoothAnimationProps) {
  const elementRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<gsap.core.Timeline | null>(null);

  // Check for reduced motion preference
  const prefersReducedMotion = typeof window !== 'undefined' &&
    window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  const shouldReduceMotion = prefersReducedMotion && !forceAnimation;

  // If reduced motion is preferred, use 'none' animation type
  const animationType = shouldReduceMotion || !shouldAnimate ? 'none' : type;

  useEffect(() => {
    if (!elementRef.current || animationType === 'none') return;

    const element = elementRef.current;
    const config = getAnimationConfig(animationType);

    // Kill any existing animations for performance
    gsap.killTweensOf(element);

    // Set initial state with hardware acceleration
    gsap.set(element, {
      ...config.from,
      force3D: true,
      willChange: 'transform, opacity'
    });

    // Create optimized timeline for 60fps
    const tl = gsap.timeline({
      delay: shouldReduceMotion ? 0 : delay,
      repeat: repeat > 0 ? repeat : 0,
      onComplete: () => {
        // Reset will-change for performance
        gsap.set(element, { willChange: 'auto' });
        if (onComplete) onComplete();
      }
    });

    // Add animation to timeline with 60fps optimization
    tl.to(element, {
      ...config.to,
      duration: shouldReduceMotion ? 0 : duration,
      ease: "power2.out",
      force3D: true,
      willChange: 'transform, opacity',
      // Optimize for 60fps by using transform3d
      transformOrigin: "center center",
    });

    animationRef.current = tl;

    // Cleanup function
    return () => {
      if (animationRef.current) {
        animationRef.current.kill();
      }
      // Reset will-change
      gsap.set(element, { willChange: 'auto' });
    };
  }, [animationType, duration, delay, repeat, shouldReduceMotion, onComplete]);

  return (
    <div
      ref={elementRef}
      className={cn(className)}
      {...props}
    >
      {children}
    </div>
  );
}

export function FadeIn({ children, ...props }: Omit<SmoothAnimationProps, 'type'>) {
  return <SmoothAnimation type="fade" {...props}>{children}</SmoothAnimation>;
}

export function SlideUp({ children, ...props }: Omit<SmoothAnimationProps, 'type'>) {
  return <SmoothAnimation type="slide-up" {...props}>{children}</SmoothAnimation>;
}

export function SlideDown({ children, ...props }: Omit<SmoothAnimationProps, 'type'>) {
  return <SmoothAnimation type="slide-down" {...props}>{children}</SmoothAnimation>;
}

export function SlideLeft({ children, ...props }: Omit<SmoothAnimationProps, 'type'>) {
  return <SmoothAnimation type="slide-left" {...props}>{children}</SmoothAnimation>;
}

export function SlideRight({ children, ...props }: Omit<SmoothAnimationProps, 'type'>) {
  return <SmoothAnimation type="slide-right" {...props}>{children}</SmoothAnimation>;
}

export function ScaleIn({ children, ...props }: Omit<SmoothAnimationProps, 'type'>) {
  return <SmoothAnimation type="scale" {...props}>{children}</SmoothAnimation>;
}

export function SlideLeft({ children, ...props }: Omit<SmoothAnimationProps, 'type'>) {
  return <SmoothAnimation type="slide-left" {...props}>{children}</SmoothAnimation>;
}

export function SlideRight({ children, ...props }: Omit<SmoothAnimationProps, 'type'>) {
  return <SmoothAnimation type="slide-right" {...props}>{children}</SmoothAnimation>;
}

export function ScaleIn({ children, ...props }: Omit<SmoothAnimationProps, 'type'>) {
  return <SmoothAnimation type="scale" {...props}>{children}</SmoothAnimation>;
} 