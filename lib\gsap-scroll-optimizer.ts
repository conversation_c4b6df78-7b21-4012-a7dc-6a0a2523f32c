'use client';

import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register ScrollTrigger plugin
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

// Professional GSAP Scroll Optimizer for ultra-smooth scrolling
export class GSAPScrollOptimizer {
  private static initialized = false;
  private static scrollElements: Map<Element, gsap.core.Tween> = new Map();
  private static rafId: number | null = null;

  // Initialize professional scroll optimizations
  static init() {
    if (this.initialized || typeof window === 'undefined') return;

    // Configure ScrollTrigger for optimal performance
    ScrollTrigger.config({
      autoRefreshEvents: "visibilitychange,DOMContentLoaded,load",
      ignoreMobileResize: true
    });

    // Setup smooth scrolling
    this.setupSmoothScrolling();
    
    // Setup scroll-triggered animations
    this.setupScrollAnimations();
    
    // Setup performance optimizations
    this.setupScrollPerformance();

    this.initialized = true;
  }

  // Setup ultra-smooth scrolling
  private static setupSmoothScrolling() {
    // Optimize scroll behavior
    document.documentElement.style.scrollBehavior = 'auto'; // Let GSAP handle smoothness
    
    // Add smooth scroll to anchor links
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      const link = target.closest('a[href^="#"]') as HTMLAnchorElement;
      
      if (link && link.hash) {
        e.preventDefault();
        const targetElement = document.querySelector(link.hash);
        
        if (targetElement) {
          gsap.to(window, {
            duration: 1.2,
            scrollTo: { 
              y: targetElement, 
              offsetY: 80,
              autoKill: false
            },
            ease: "power2.inOut"
          });
        }
      }
    });
  }

  // Setup professional scroll-triggered animations
  private static setupScrollAnimations() {
    // Fade in animations on scroll
    gsap.utils.toArray('.gsap-fade-in-scroll').forEach((element: any) => {
      gsap.fromTo(element, 
        { 
          opacity: 0, 
          y: 50,
          willChange: 'transform, opacity'
        },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          ease: "power2.out",
          force3D: true,
          scrollTrigger: {
            trigger: element,
            start: "top 85%",
            end: "bottom 15%",
            toggleActions: "play none none reverse",
            onComplete: () => {
              gsap.set(element, { willChange: 'auto' });
            }
          }
        }
      );
    });

    // Slide animations on scroll
    gsap.utils.toArray('.gsap-slide-in-scroll').forEach((element: any) => {
      const direction = element.dataset.direction || 'up';
      const distance = parseInt(element.dataset.distance) || 100;
      
      let fromProps: any = { opacity: 0 };
      let toProps: any = { opacity: 1 };
      
      switch (direction) {
        case 'up':
          fromProps.y = distance;
          toProps.y = 0;
          break;
        case 'down':
          fromProps.y = -distance;
          toProps.y = 0;
          break;
        case 'left':
          fromProps.x = distance;
          toProps.x = 0;
          break;
        case 'right':
          fromProps.x = -distance;
          toProps.x = 0;
          break;
      }

      gsap.fromTo(element, 
        { 
          ...fromProps,
          willChange: 'transform, opacity'
        },
        {
          ...toProps,
          duration: 1.2,
          ease: "power2.out",
          force3D: true,
          scrollTrigger: {
            trigger: element,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse",
            onComplete: () => {
              gsap.set(element, { willChange: 'auto' });
            }
          }
        }
      );
    });

    // Stagger animations on scroll
    gsap.utils.toArray('.gsap-stagger-scroll').forEach((container: any) => {
      const children = container.children;
      const staggerAmount = parseFloat(container.dataset.stagger) || 0.1;
      
      gsap.fromTo(children, 
        { 
          opacity: 0, 
          y: 50,
          willChange: 'transform, opacity'
        },
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
          ease: "power2.out",
          force3D: true,
          stagger: staggerAmount,
          scrollTrigger: {
            trigger: container,
            start: "top 85%",
            end: "bottom 15%",
            toggleActions: "play none none reverse",
            onComplete: () => {
              gsap.set(children, { willChange: 'auto' });
            }
          }
        }
      );
    });

    // Scale animations on scroll
    gsap.utils.toArray('.gsap-scale-scroll').forEach((element: any) => {
      gsap.fromTo(element, 
        { 
          opacity: 0, 
          scale: 0.8,
          willChange: 'transform, opacity'
        },
        {
          opacity: 1,
          scale: 1,
          duration: 1,
          ease: "back.out(1.7)",
          force3D: true,
          scrollTrigger: {
            trigger: element,
            start: "top 85%",
            end: "bottom 15%",
            toggleActions: "play none none reverse",
            onComplete: () => {
              gsap.set(element, { willChange: 'auto' });
            }
          }
        }
      );
    });
  }

  // Setup scroll performance optimizations
  private static setupScrollPerformance() {
    let ticking = false;
    
    // Throttled scroll handler
    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          // Update scroll-dependent animations
          this.updateScrollAnimations();
          ticking = false;
        });
        ticking = true;
      }
    };

    // Optimized scroll listener
    window.addEventListener('scroll', handleScroll, { 
      passive: true,
      capture: false
    });

    // Optimize for mobile
    if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
      ScrollTrigger.config({
        ignoreMobileResize: true,
        autoRefreshEvents: "visibilitychange,DOMContentLoaded,load"
      });
    }
  }

  // Update scroll animations efficiently
  private static updateScrollAnimations() {
    // Batch DOM reads and writes
    const scrollY = window.scrollY;
    const windowHeight = window.innerHeight;
    
    // Update parallax elements
    const parallaxElements = document.querySelectorAll('.gsap-parallax');
    parallaxElements.forEach((element: any) => {
      const speed = parseFloat(element.dataset.speed) || 0.5;
      const rect = element.getBoundingClientRect();
      
      if (rect.bottom >= 0 && rect.top <= windowHeight) {
        const yPos = -(scrollY * speed);
        gsap.set(element, { 
          y: yPos,
          force3D: true
        });
      }
    });
  }

  // Add smooth scroll to element
  static addSmoothScroll(element: HTMLElement | string, options: any = {}) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    const scrollTween = gsap.to(target, {
      scrollTo: { y: 0 },
      duration: options.duration || 1,
      ease: options.ease || "power2.inOut",
      paused: true
    });

    this.scrollElements.set(target, scrollTween);
    return scrollTween;
  }

  // Scroll to element smoothly
  static scrollToElement(element: HTMLElement | string, options: any = {}) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    return gsap.to(window, {
      duration: options.duration || 1.2,
      scrollTo: { 
        y: target, 
        offsetY: options.offset || 80,
        autoKill: false
      },
      ease: options.ease || "power2.inOut",
      onComplete: options.onComplete
    });
  }

  // Create parallax effect
  static createParallax(element: HTMLElement | string, speed: number = 0.5) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    target.setAttribute('data-speed', speed.toString());
    target.classList.add('gsap-parallax');
  }

  // Refresh ScrollTrigger (useful for dynamic content)
  static refresh() {
    if (typeof window !== 'undefined' && ScrollTrigger) {
      ScrollTrigger.refresh();
    }
  }

  // Kill all scroll animations
  static killAll() {
    this.scrollElements.forEach(tween => tween.kill());
    this.scrollElements.clear();
    
    if (typeof window !== 'undefined' && ScrollTrigger) {
      ScrollTrigger.killAll();
    }
  }

  // Cleanup
  static cleanup() {
    this.killAll();
    
    if (this.rafId) {
      cancelAnimationFrame(this.rafId);
      this.rafId = null;
    }
    
    this.initialized = false;
  }
}

// Auto-initialize
if (typeof window !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => GSAPScrollOptimizer.init());
  } else {
    GSAPScrollOptimizer.init();
  }
}

export default GSAPScrollOptimizer;
