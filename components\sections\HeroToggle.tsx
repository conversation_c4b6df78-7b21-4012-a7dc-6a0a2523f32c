import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface HeroToggleProps {
  currentHero: 'original' | 'business';
  onToggle: (hero: 'original' | 'business') => void;
  isHeroPage?: boolean; // Add prop to identify if it's the Hero page
}

export default function HeroToggle({ currentHero, onToggle, isHeroPage = false }: HeroToggleProps) {
  // Track previous hero state for animations
  const [prevHero, setPrevHero] = useState<'original' | 'business'>(currentHero);
  const toggleContainerRef = useRef<HTMLDivElement>(null);
  
  // Detect direction of the toggle for animation
  const direction = prevHero === 'original' && currentHero === 'business' ? 1 : -1;
  
  // Update previous hero when current changes
  useEffect(() => {
    setPrevHero(currentHero);
  }, [currentHero]);

  // Handle toggle with animation
  const handleToggle = (hero: 'original' | 'business') => {
    if (hero === currentHero) return;
    setPrevHero(currentHero);
    onToggle(hero);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: 0.2 }}
      className={`fixed left-0 right-0 w-full flex justify-center z-[60] px-4 ${
        isHeroPage ? 'top-16 sm:top-8' : 'top-16 sm:top-19'
      }`}
      style={{ transform: 'translate3d(0, 0, 0)' }}
    >
      <div className="relative">
        {/* Enhanced glow effect with animated pulse */}
        <motion.div 
          className="absolute inset-0 -m-1 rounded-full blur-lg opacity-80"
          animate={{
            opacity: [0.6, 0.8, 0.6],
            scale: [1, 1.02, 1],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          style={{
            background: 'radial-gradient(circle, rgba(139, 92, 246, 0.5), rgba(99, 102, 241, 0.3), rgba(59, 130, 246, 0.2))',
          }}
        ></motion.div>
        
        <div
          ref={toggleContainerRef}
          className={`flex p-1 bg-black/95 backdrop-blur-xl rounded-full border border-white/20 shadow-lg relative overflow-hidden ${
            isHeroPage ? 'scale-90 sm:scale-110' : 'scale-90 sm:scale-100'
          }`}
          style={{
            transform: 'translate3d(0, 0, 0)',
            willChange: 'transform'
          }}
        >
          {/* SwitchFlow animation for background transitions */}
          <AnimatePresence mode="wait">
            <motion.div 
              key={`background-${currentHero}`}
              initial={{ opacity: 0, x: direction * 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -direction * 20 }}
              transition={{ 
                type: "spring", 
                stiffness: 300, 
                damping: 30,
                mass: 1
              }}
              className="absolute inset-0 z-0"
            >
              {/* Subtle animated background patterns */}
              {currentHero === 'original' && (
                <motion.div 
                  className="absolute inset-0 opacity-10"
                  animate={{
                    backgroundPosition: ['0% 0%', '100% 100%'],
                  }}
                  transition={{
                    duration: 20,
                    repeat: Infinity,
                    repeatType: "reverse",
                  }}
                  style={{
                    backgroundImage: 'radial-gradient(circle at 10% 30%, rgba(255, 255, 255, 0.2) 0%, transparent 20%), radial-gradient(circle at 90% 70%, rgba(255, 255, 255, 0.2) 0%, transparent 20%)',
                    backgroundSize: '200% 200%',
                  }}
                />
              )}
              {currentHero === 'business' && (
                <motion.div 
                  className="absolute inset-0 opacity-10"
                  animate={{
                    backgroundPosition: ['0% 0%', '100% 100%'],
                  }}
                  transition={{
                    duration: 20,
                    repeat: Infinity,
                    repeatType: "reverse",
                  }}
                  style={{
                    backgroundImage: 'linear-gradient(45deg, rgba(139, 92, 246, 0.2) 0%, transparent 40%), linear-gradient(135deg, rgba(79, 70, 229, 0.2) 0%, transparent 40%)',
                    backgroundSize: '200% 200%',
                  }}
                />
              )}
            </motion.div>
          </AnimatePresence>

          <button
            onClick={() => handleToggle('original')}
            className={`relative px-3 py-1.5 text-xs rounded-full transition-all duration-300
                      flex items-center gap-1 z-10 touch-manipulation
                      ${currentHero === 'original'
                        ? 'font-medium'
                        : 'text-white/80 hover:text-white/100 active:text-white'}`}
            aria-label="Switch to Meet-ALI hero"
            style={{
              WebkitTapHighlightColor: 'transparent',
              transform: 'translate3d(0, 0, 0)'
            }}
          >
            {/* Text with emoji that animates on state change */}
            <AnimatePresence mode="wait">
              <motion.div
                key={`text-${currentHero === 'original' ? 'active' : 'inactive'}-original`}
                initial={{ y: currentHero === 'original' ? 10 : 0, opacity: currentHero === 'original' ? 0 : 1 }}
                animate={{ y: 0, opacity: 1 }}
                exit={{ y: -10, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="flex items-center gap-1.5"
              >
                <span>{currentHero === 'original' ? '👋' : ''}</span>
                <span className={currentHero === 'original' ? 'text-black' : ''}>Meet-ALI&TEAM</span>
              </motion.div>
            </AnimatePresence>
            
            {currentHero === 'original' && (
              <motion.div 
                layoutId="activeToggle"
                className="absolute inset-0 bg-white rounded-full -z-10"
                initial={false}
                transition={{ 
                  type: "spring", 
                  stiffness: 500, 
                  damping: 30,
                  mass: 1
                }}
              />
            )}
          </button>
          
          <button
            onClick={() => handleToggle('business')}
            className={`relative px-3 py-1.5 text-xs rounded-full transition-all duration-300
                      flex items-center gap-1 z-10 touch-manipulation
                      ${currentHero === 'business'
                        ? 'font-medium'
                        : 'text-white/80 hover:text-white/100 active:text-white'}`}
            aria-label="Switch to business hero"
            style={{
              WebkitTapHighlightColor: 'transparent',
              transform: 'translate3d(0, 0, 0)'
            }}
          >
            {/* Text that animates on state change */}
            <AnimatePresence mode="wait">
              <motion.div
                key={`text-${currentHero === 'business' ? 'active' : 'inactive'}-business`}
                initial={{ y: currentHero === 'business' ? 10 : 0, opacity: currentHero === 'business' ? 0 : 1 }}
                animate={{ y: 0, opacity: 1 }}
                exit={{ y: -10, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="flex items-center gap-1.5"
              >
                <span>{currentHero === 'business' ? '🧠' : ''}</span>
                <span className={currentHero === 'business' ? 'text-white' : ''}>OUR-Details</span>
              </motion.div>
            </AnimatePresence>
            
            {currentHero === 'business' && (
              <motion.div 
                layoutId="activeToggle"
                className="absolute inset-0 rounded-full -z-10"
                style={{
                  background: 'linear-gradient(to right, rgb(139, 92, 246), rgb(79, 70, 229))'
                }}
                initial={false}
                transition={{ 
                  type: "spring", 
                  stiffness: 500, 
                  damping: 30,
                  mass: 1
                }}
              />
            )}
          </button>
        </div>
        
        {/* Enhanced shadow effect for depth */}
        <div className="absolute inset-0 -z-10 rounded-full blur-md opacity-30"
             style={{
               background: currentHero === 'original' 
                 ? 'radial-gradient(circle, rgba(255, 255, 255, 0.8), transparent 70%)' 
                 : 'radial-gradient(circle, rgba(139, 92, 246, 0.8), transparent 70%)',
               transform: 'translateY(4px) scale(0.9)',
             }}></div>
      </div>
    </motion.div>
  );
} 