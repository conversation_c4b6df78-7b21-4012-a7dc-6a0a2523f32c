'use client';

import { gsap } from 'gsap';

// GSAP-based hover effects to replace CSS transitions
export class GSAPHoverEffects {
  
  // Lift effect (replaces hover:-translate-y-0.5)
  static setupLiftEffect(element: HTMLElement | string, options: any = {}) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    const liftTween = gsap.to(target, {
      y: -5,
      duration: 0.3,
      ease: "power2.out",
      paused: true,
      force3D: true,
      ...options
    });

    const resetTween = gsap.to(target, {
      y: 0,
      duration: 0.3,
      ease: "power2.out",
      paused: true,
      force3D: true
    });

    target.addEventListener('mouseenter', () => {
      resetTween.pause();
      liftTween.restart();
    });

    target.addEventListener('mouseleave', () => {
      liftTween.pause();
      resetTween.restart();
    });

    return { liftTween, resetTween };
  }

  // Scale effect
  static setupScaleEffect(element: HTMLElement | string, options: any = {}) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    const scaleTween = gsap.to(target, {
      scale: 1.05,
      duration: 0.3,
      ease: "power2.out",
      paused: true,
      force3D: true,
      ...options
    });

    const resetTween = gsap.to(target, {
      scale: 1,
      duration: 0.3,
      ease: "power2.out",
      paused: true,
      force3D: true
    });

    target.addEventListener('mouseenter', () => {
      resetTween.pause();
      scaleTween.restart();
    });

    target.addEventListener('mouseleave', () => {
      scaleTween.pause();
      resetTween.restart();
    });

    return { scaleTween, resetTween };
  }

  // Glow effect
  static setupGlowEffect(element: HTMLElement | string, options: any = {}) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    const glowTween = gsap.to(target, {
      boxShadow: "0 0 20px 5px rgba(147, 51, 234, 0.4)",
      duration: 0.3,
      ease: "power2.out",
      paused: true,
      ...options
    });

    const resetTween = gsap.to(target, {
      boxShadow: "0 0 0px 0px rgba(147, 51, 234, 0)",
      duration: 0.3,
      ease: "power2.out",
      paused: true
    });

    target.addEventListener('mouseenter', () => {
      resetTween.pause();
      glowTween.restart();
    });

    target.addEventListener('mouseleave', () => {
      glowTween.pause();
      resetTween.restart();
    });

    return { glowTween, resetTween };
  }

  // Rotate effect
  static setupRotateEffect(element: HTMLElement | string, options: any = {}) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    const rotateTween = gsap.to(target, {
      rotation: 5,
      duration: 0.3,
      ease: "power2.out",
      paused: true,
      force3D: true,
      ...options
    });

    const resetTween = gsap.to(target, {
      rotation: 0,
      duration: 0.3,
      ease: "power2.out",
      paused: true,
      force3D: true
    });

    target.addEventListener('mouseenter', () => {
      resetTween.pause();
      rotateTween.restart();
    });

    target.addEventListener('mouseleave', () => {
      rotateTween.pause();
      resetTween.restart();
    });

    return { rotateTween, resetTween };
  }

  // Magnetic effect (follows cursor)
  static setupMagneticEffect(element: HTMLElement | string, options: any = {}) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    const strength = options.strength || 0.3;
    let isHovering = false;

    const handleMouseMove = (e: MouseEvent) => {
      if (!isHovering) return;

      const rect = (target as HTMLElement).getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      
      const deltaX = (e.clientX - centerX) * strength;
      const deltaY = (e.clientY - centerY) * strength;

      gsap.to(target, {
        x: deltaX,
        y: deltaY,
        duration: 0.3,
        ease: "power2.out",
        force3D: true
      });
    };

    const handleMouseEnter = () => {
      isHovering = true;
    };

    const handleMouseLeave = () => {
      isHovering = false;
      gsap.to(target, {
        x: 0,
        y: 0,
        duration: 0.5,
        ease: "power2.out",
        force3D: true
      });
    };

    target.addEventListener('mouseenter', handleMouseEnter);
    target.addEventListener('mouseleave', handleMouseLeave);
    document.addEventListener('mousemove', handleMouseMove);

    return {
      destroy: () => {
        target.removeEventListener('mouseenter', handleMouseEnter);
        target.removeEventListener('mouseleave', handleMouseLeave);
        document.removeEventListener('mousemove', handleMouseMove);
      }
    };
  }

  // Button press effect
  static setupButtonPressEffect(element: HTMLElement | string, options: any = {}) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    const pressTween = gsap.to(target, {
      scale: 0.95,
      duration: 0.1,
      ease: "power2.out",
      paused: true,
      force3D: true,
      ...options
    });

    const releaseTween = gsap.to(target, {
      scale: 1,
      duration: 0.2,
      ease: "back.out(1.7)",
      paused: true,
      force3D: true
    });

    target.addEventListener('mousedown', () => {
      releaseTween.pause();
      pressTween.restart();
    });

    target.addEventListener('mouseup', () => {
      pressTween.pause();
      releaseTween.restart();
    });

    target.addEventListener('mouseleave', () => {
      pressTween.pause();
      releaseTween.restart();
    });

    return { pressTween, releaseTween };
  }

  // Ripple effect on click
  static setupRippleEffect(element: HTMLElement | string, options: any = {}) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    const handleClick = (e: MouseEvent) => {
      const rect = (target as HTMLElement).getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      const ripple = document.createElement('div');
      ripple.style.position = 'absolute';
      ripple.style.left = x + 'px';
      ripple.style.top = y + 'px';
      ripple.style.width = '0px';
      ripple.style.height = '0px';
      ripple.style.borderRadius = '50%';
      ripple.style.background = options.color || 'rgba(255, 255, 255, 0.3)';
      ripple.style.pointerEvents = 'none';
      ripple.style.transform = 'translate(-50%, -50%)';

      (target as HTMLElement).appendChild(ripple);

      gsap.to(ripple, {
        width: '200px',
        height: '200px',
        opacity: 0,
        duration: 0.6,
        ease: "power2.out",
        onComplete: () => {
          ripple.remove();
        }
      });
    };

    target.addEventListener('click', handleClick);

    return {
      destroy: () => {
        target.removeEventListener('click', handleClick);
      }
    };
  }

  // Auto-setup common hover effects based on class names
  static autoSetup() {
    // Setup lift effects
    document.querySelectorAll('.gsap-hover-lift').forEach(element => {
      this.setupLiftEffect(element as HTMLElement);
    });

    // Setup scale effects
    document.querySelectorAll('.gsap-hover-scale').forEach(element => {
      this.setupScaleEffect(element as HTMLElement);
    });

    // Setup glow effects
    document.querySelectorAll('.gsap-hover-glow').forEach(element => {
      this.setupGlowEffect(element as HTMLElement);
    });

    // Setup rotate effects
    document.querySelectorAll('.gsap-hover-rotate').forEach(element => {
      this.setupRotateEffect(element as HTMLElement);
    });

    // Setup magnetic effects
    document.querySelectorAll('.gsap-magnetic').forEach(element => {
      this.setupMagneticEffect(element as HTMLElement);
    });

    // Setup button press effects
    document.querySelectorAll('.gsap-button-press').forEach(element => {
      this.setupButtonPressEffect(element as HTMLElement);
    });

    // Setup ripple effects
    document.querySelectorAll('.gsap-ripple').forEach(element => {
      this.setupRippleEffect(element as HTMLElement);
    });
  }
}

// Auto-initialize when DOM is ready
if (typeof window !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => GSAPHoverEffects.autoSetup());
  } else {
    GSAPHoverEffects.autoSetup();
  }
}

export default GSAPHoverEffects;
