import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// Define protected admin routes
const PROTECTED_ADMIN_ROUTES = [
  '/hasnaat',
  '/hasnaat/projects',
  '/hasnaat/projects/new',
  '/hasnaat/projects/edit',
  '/hasnaat/database',
  '/hasnaat/database/test',
  '/hasnaat/team',
  '/hasnaat/reviews',
  '/hasnaat/questions',
  '/hasnaat/command-room'
]

// Define public admin routes (no authentication required)
const PUBLIC_ADMIN_ROUTES = [
  '/hasnaat/login'
]

// Legacy admin paths that should be permanently blocked
const LEGACY_ADMIN_PATHS = [
  '/admin',
  '/admin/login',
  '/admin/projects',
  '/admin/team',
  '/admin/reviews',
  '/admin/database',
  '/admin/command-room'
]

// Security helper functions
function isLegacyAdminPath(pathname: string): boolean {
  return pathname.startsWith('/admin')
}

function isAccessBlocked(request: NextRequest): boolean {
  // Check for security block cookie
  const securityBlock = request.cookies.get('security-block')
  if (securityBlock && securityBlock.value === 'true') {
    return true
  }

  // Check for failed attempts cookie
  const failedAttempts = request.cookies.get('failed-attempts')
  if (failedAttempts && parseInt(failedAttempts.value) >= 2) {
    return true
  }

  return false
}

function logSecurityViolation(type: string, details: any): void {
  const timestamp = new Date().toISOString()
  console.warn(`🚨 SECURITY VIOLATION [${type.toUpperCase()}] at ${timestamp}:`, details)
}

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  const clientIP = request.ip || request.headers.get('x-forwarded-for') || 'unknown'
  const userAgent = request.headers.get('user-agent') || 'unknown'

  // 🚨 SECURITY CHECK 1: Block all legacy admin paths permanently
  if (isLegacyAdminPath(pathname)) {
    logSecurityViolation('legacy_path_access', {
      pathname,
      clientIP,
      userAgent,
      timestamp: new Date().toISOString()
    })

    // Return 404 response for legacy admin paths
    return new NextResponse(null, { status: 404 })
  }

  // Check if this is a hasnaat admin route
  const isAdminRoute = pathname.startsWith('/hasnaat')

  if (!isAdminRoute) {
    // Allow all non-admin routes
    return NextResponse.next()
  }

  // 🚨 SECURITY CHECK 2: Check if access is blocked due to failed attempts
  if (isAccessBlocked(request)) {
    logSecurityViolation('blocked_access_attempt', {
      pathname,
      clientIP,
      userAgent,
      timestamp: new Date().toISOString()
    })

    // Return 404 response for blocked access
    return new NextResponse(null, { status: 404 })
  }

  // Check if this is a public admin route (login page)
  const isPublicAdminRoute = PUBLIC_ADMIN_ROUTES.some(route =>
    pathname === route || pathname.startsWith(route + '/')
  )

  if (isPublicAdminRoute) {
    // For login page, still check if access is blocked
    if (isAccessBlocked(request)) {
      return new NextResponse(null, { status: 404 })
    }

    // Allow access to login page if not blocked
    return NextResponse.next()
  }

  // Check if this is a protected admin route
  const isProtectedRoute = PROTECTED_ADMIN_ROUTES.some(route =>
    pathname === route || pathname.startsWith(route + '/')
  )

  if (isProtectedRoute) {
    // Check for authentication cookie
    const authCookie = request.cookies.get('admin-auth')

    if (!authCookie || authCookie.value !== 'true') {
      // Check if access is blocked before redirecting
      if (isAccessBlocked(request)) {
        return new NextResponse(null, { status: 404 })
      }

      // Redirect to login if not authenticated and not blocked
      const loginUrl = new URL('/hasnaat/login', request.url)
      loginUrl.searchParams.set('redirect', pathname)

      console.log(`🔒 Unauthorized access attempt to ${pathname}, redirecting to login`)

      return NextResponse.redirect(loginUrl)
    }

    // User is authenticated, allow access
    console.log(`✅ Authenticated access to ${pathname}`)
    return NextResponse.next()
  }

  // For any other hasnaat routes not explicitly defined, require authentication
  const authCookie = request.cookies.get('admin-auth')

  if (!authCookie || authCookie.value !== 'true') {
    // Check if access is blocked
    if (isAccessBlocked(request)) {
      return new NextResponse(null, { status: 404 })
    }

    const loginUrl = new URL('/hasnaat/login', request.url)
    loginUrl.searchParams.set('redirect', pathname)

    console.log(`🔒 Unauthorized access attempt to ${pathname}, redirecting to login`)

    return NextResponse.redirect(loginUrl)
  }

  return NextResponse.next()
}

// Configure which routes this middleware should run on
export const config = {
  matcher: [
    /*
     * Match all admin routes (both legacy and current) except:
     * - api routes (handled separately)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/admin/:path*',
    '/hasnaat/:path*'
  ]
}
