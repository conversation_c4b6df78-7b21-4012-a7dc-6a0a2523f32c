'use client';

import { gsap } from 'gsap';
import GSAP60FPSOptimizer from './gsap-60fps-optimizer';
import GSAPChatbotProtection from './gsap-chatbot-protection';

// GSAP Testing Suite - Comprehensive testing for GSAP animations
export class GSAPTestingSuite {
  private static testResults: any[] = [];
  private static isRunning = false;

  // Run comprehensive GSAP tests
  static async runAllTests(): Promise<any> {
    if (this.isRunning) return { error: 'Tests already running' };
    
    this.isRunning = true;
    this.testResults = [];
    
    console.log('🚀 Starting GSAP Testing Suite...');
    
    try {
      // Performance tests
      await this.testPerformance();
      
      // Animation functionality tests
      await this.testAnimationFunctionality();
      
      // Mobile responsiveness tests
      await this.testMobileResponsiveness();
      
      // Chatbot protection tests
      await this.testChatbotProtection();
      
      // Memory leak tests
      await this.testMemoryLeaks();
      
      // Cross-browser compatibility tests
      await this.testCrossBrowserCompatibility();
      
      const summary = this.generateTestSummary();
      console.log('✅ GSAP Testing Suite completed:', summary);
      
      return summary;
    } catch (error) {
      console.error('❌ GSAP Testing Suite failed:', error);
      return { error: error.message, results: this.testResults };
    } finally {
      this.isRunning = false;
    }
  }

  // Test performance and FPS
  private static async testPerformance(): Promise<void> {
    console.log('🔍 Testing performance...');
    
    const startTime = performance.now();
    let frameCount = 0;
    let fps = 0;
    
    // Measure FPS over 2 seconds
    const measureFPS = () => {
      frameCount++;
      const elapsed = performance.now() - startTime;
      
      if (elapsed >= 2000) {
        fps = Math.round((frameCount * 1000) / elapsed);
        
        this.testResults.push({
          test: 'Performance - FPS',
          result: fps >= 30 ? 'PASS' : 'FAIL',
          value: fps,
          expected: '≥30 FPS',
          details: `Measured ${fps} FPS over 2 seconds`
        });
        
        return;
      }
      
      requestAnimationFrame(measureFPS);
    };
    
    requestAnimationFrame(measureFPS);
    
    // Wait for FPS measurement
    await new Promise(resolve => setTimeout(resolve, 2100));
    
    // Test GSAP performance stats
    const perfStats = GSAP60FPSOptimizer.getPerformanceStats();
    
    this.testResults.push({
      test: 'Performance - GSAP Stats',
      result: 'INFO',
      value: perfStats,
      details: 'Current GSAP performance statistics'
    });
  }

  // Test animation functionality
  private static async testAnimationFunctionality(): Promise<void> {
    console.log('🎬 Testing animation functionality...');
    
    // Create test element
    const testElement = document.createElement('div');
    testElement.id = 'gsap-test-element';
    testElement.style.cssText = `
      position: fixed;
      top: -100px;
      left: -100px;
      width: 50px;
      height: 50px;
      background: red;
      opacity: 0;
      z-index: -1;
    `;
    document.body.appendChild(testElement);
    
    try {
      // Test fade in animation
      await this.testFadeInAnimation(testElement);
      
      // Test slide up animation
      await this.testSlideUpAnimation(testElement);
      
      // Test scale animation
      await this.testScaleAnimation(testElement);
      
      // Test hover effects
      await this.testHoverEffects(testElement);
      
    } finally {
      // Cleanup test element
      testElement.remove();
    }
  }

  // Test fade in animation
  private static async testFadeInAnimation(element: HTMLElement): Promise<void> {
    return new Promise((resolve) => {
      gsap.set(element, { opacity: 0 });
      
      const tween = gsap.to(element, {
        opacity: 1,
        duration: 0.5,
        onComplete: () => {
          const opacity = parseFloat(window.getComputedStyle(element).opacity);
          
          this.testResults.push({
            test: 'Animation - Fade In',
            result: opacity > 0.9 ? 'PASS' : 'FAIL',
            value: opacity,
            expected: '≥0.9',
            details: 'Fade in animation completed successfully'
          });
          
          resolve();
        }
      });
    });
  }

  // Test slide up animation
  private static async testSlideUpAnimation(element: HTMLElement): Promise<void> {
    return new Promise((resolve) => {
      gsap.set(element, { y: 50 });
      
      const tween = gsap.to(element, {
        y: 0,
        duration: 0.5,
        onComplete: () => {
          const transform = window.getComputedStyle(element).transform;
          const yValue = transform.includes('matrix') ? 
            parseFloat(transform.split(',')[5]) || 0 : 0;
          
          this.testResults.push({
            test: 'Animation - Slide Up',
            result: Math.abs(yValue) < 5 ? 'PASS' : 'FAIL',
            value: yValue,
            expected: '≈0',
            details: 'Slide up animation completed successfully'
          });
          
          resolve();
        }
      });
    });
  }

  // Test scale animation
  private static async testScaleAnimation(element: HTMLElement): Promise<void> {
    return new Promise((resolve) => {
      gsap.set(element, { scale: 0.5 });
      
      const tween = gsap.to(element, {
        scale: 1,
        duration: 0.5,
        onComplete: () => {
          const transform = window.getComputedStyle(element).transform;
          const scaleMatch = transform.match(/matrix\(([^,]+)/);
          const scale = scaleMatch ? parseFloat(scaleMatch[1]) : 1;
          
          this.testResults.push({
            test: 'Animation - Scale',
            result: Math.abs(scale - 1) < 0.1 ? 'PASS' : 'FAIL',
            value: scale,
            expected: '≈1',
            details: 'Scale animation completed successfully'
          });
          
          resolve();
        }
      });
    });
  }

  // Test hover effects
  private static async testHoverEffects(element: HTMLElement): Promise<void> {
    // Simulate hover effect setup
    let hoverWorking = false;
    
    const hoverTween = gsap.to(element, {
      y: -5,
      duration: 0.3,
      paused: true,
      onComplete: () => {
        hoverWorking = true;
      }
    });
    
    hoverTween.play();
    
    await new Promise(resolve => setTimeout(resolve, 400));
    
    this.testResults.push({
      test: 'Animation - Hover Effects',
      result: hoverWorking ? 'PASS' : 'FAIL',
      value: hoverWorking,
      expected: 'true',
      details: 'Hover effect animation system working'
    });
  }

  // Test mobile responsiveness
  private static async testMobileResponsiveness(): Promise<void> {
    console.log('📱 Testing mobile responsiveness...');
    
    // Simulate mobile viewport
    const originalWidth = window.innerWidth;
    const originalUserAgent = navigator.userAgent;
    
    // Test mobile detection
    const isMobileDetected = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    
    this.testResults.push({
      test: 'Mobile - Detection',
      result: 'INFO',
      value: isMobileDetected,
      details: 'Mobile device detection status'
    });
    
    // Test reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    this.testResults.push({
      test: 'Mobile - Reduced Motion',
      result: 'INFO',
      value: prefersReducedMotion,
      details: 'User prefers reduced motion setting'
    });
    
    // Test performance mode adjustment
    const perfStats = GSAP60FPSOptimizer.getPerformanceStats();
    
    this.testResults.push({
      test: 'Mobile - Performance Mode',
      result: 'INFO',
      value: perfStats.performanceMode,
      details: 'Current performance mode based on device capabilities'
    });
  }

  // Test chatbot protection
  private static async testChatbotProtection(): Promise<void> {
    console.log('🤖 Testing chatbot protection...');
    
    // Check if chatbot protection is working
    const protectedElements = GSAPChatbotProtection.getProtectedElements();
    
    this.testResults.push({
      test: 'Chatbot - Protection Active',
      result: protectedElements.length >= 0 ? 'PASS' : 'FAIL',
      value: protectedElements.length,
      expected: '≥0',
      details: `${protectedElements.length} chatbot elements protected`
    });
    
    // Verify chatbot functionality
    const chatbotFunctional = GSAPChatbotProtection.verifyChatbotFunctionality();
    
    this.testResults.push({
      test: 'Chatbot - Functionality',
      result: chatbotFunctional ? 'PASS' : 'FAIL',
      value: chatbotFunctional,
      expected: 'true',
      details: 'Chatbot functionality verification'
    });
  }

  // Test memory leaks
  private static async testMemoryLeaks(): Promise<void> {
    console.log('🧠 Testing memory leaks...');
    
    const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;
    
    // Create and destroy multiple animations
    for (let i = 0; i < 10; i++) {
      const testEl = document.createElement('div');
      document.body.appendChild(testEl);
      
      const tween = gsap.to(testEl, {
        x: 100,
        duration: 0.1,
        onComplete: () => {
          testEl.remove();
        }
      });
    }
    
    // Wait for animations to complete
    await new Promise(resolve => setTimeout(resolve, 200));
    
    // Force garbage collection if available
    if ((window as any).gc) {
      (window as any).gc();
    }
    
    const finalMemory = (performance as any).memory?.usedJSHeapSize || 0;
    const memoryIncrease = finalMemory - initialMemory;
    
    this.testResults.push({
      test: 'Memory - Leak Detection',
      result: memoryIncrease < 1000000 ? 'PASS' : 'WARN', // Less than 1MB increase
      value: `${Math.round(memoryIncrease / 1024)}KB`,
      expected: '<1MB',
      details: 'Memory usage after animation creation/destruction cycle'
    });
  }

  // Test cross-browser compatibility
  private static async testCrossBrowserCompatibility(): Promise<void> {
    console.log('🌐 Testing cross-browser compatibility...');
    
    // Test CSS transform support
    const testEl = document.createElement('div');
    testEl.style.transform = 'translate3d(0,0,0)';
    const supportsTransform3d = testEl.style.transform !== '';
    
    this.testResults.push({
      test: 'Browser - Transform3D Support',
      result: supportsTransform3d ? 'PASS' : 'FAIL',
      value: supportsTransform3d,
      expected: 'true',
      details: 'CSS transform3d support for hardware acceleration'
    });
    
    // Test requestAnimationFrame support
    const supportsRAF = typeof requestAnimationFrame !== 'undefined';
    
    this.testResults.push({
      test: 'Browser - RequestAnimationFrame',
      result: supportsRAF ? 'PASS' : 'FAIL',
      value: supportsRAF,
      expected: 'true',
      details: 'RequestAnimationFrame API support'
    });
    
    // Test performance API support
    const supportsPerformance = typeof performance !== 'undefined' && typeof performance.now === 'function';
    
    this.testResults.push({
      test: 'Browser - Performance API',
      result: supportsPerformance ? 'PASS' : 'WARN',
      value: supportsPerformance,
      expected: 'true',
      details: 'Performance API support for timing measurements'
    });
  }

  // Generate test summary
  private static generateTestSummary(): any {
    const total = this.testResults.length;
    const passed = this.testResults.filter(r => r.result === 'PASS').length;
    const failed = this.testResults.filter(r => r.result === 'FAIL').length;
    const warnings = this.testResults.filter(r => r.result === 'WARN').length;
    const info = this.testResults.filter(r => r.result === 'INFO').length;
    
    return {
      summary: {
        total,
        passed,
        failed,
        warnings,
        info,
        success: failed === 0
      },
      results: this.testResults,
      recommendations: this.generateRecommendations()
    };
  }

  // Generate recommendations based on test results
  private static generateRecommendations(): string[] {
    const recommendations: string[] = [];
    
    const failedTests = this.testResults.filter(r => r.result === 'FAIL');
    const warningTests = this.testResults.filter(r => r.result === 'WARN');
    
    if (failedTests.length > 0) {
      recommendations.push('❌ Critical issues found that need immediate attention');
    }
    
    if (warningTests.length > 0) {
      recommendations.push('⚠️ Performance warnings detected - consider optimization');
    }
    
    const fpsTest = this.testResults.find(r => r.test === 'Performance - FPS');
    if (fpsTest && fpsTest.value < 60) {
      recommendations.push('🎯 Consider reducing animation complexity for better FPS');
    }
    
    if (failedTests.length === 0 && warningTests.length === 0) {
      recommendations.push('✅ All tests passed - GSAP animations are optimized and working correctly');
    }
    
    return recommendations;
  }

  // Get test results
  static getTestResults(): any[] {
    return this.testResults;
  }

  // Quick performance check
  static quickPerformanceCheck(): any {
    const perfStats = GSAP60FPSOptimizer.getPerformanceStats();
    const protectedElements = GSAPChatbotProtection.getProtectedElements().length;
    
    return {
      fps: perfStats.actualFPS,
      performanceMode: perfStats.performanceMode,
      activeAnimations: perfStats.activeAnimations,
      protectedElements,
      timestamp: new Date().toISOString()
    };
  }
}

// Export for console access
if (typeof window !== 'undefined') {
  (window as any).GSAPTestingSuite = GSAPTestingSuite;
}

export default GSAPTestingSuite;
