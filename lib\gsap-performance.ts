'use client';

import { gsap } from 'gsap';

// GSAP Performance Optimization Configuration
export class GSAPPerformance {
  private static initialized = false;
  private static rafId: number | null = null;
  private static pendingAnimations: (() => void)[] = [];

  // Initialize GSAP with performance optimizations
  static init() {
    if (this.initialized || typeof window === 'undefined') return;

    // Configure GSAP for maximum performance
    gsap.config({
      force3D: true,
      nullTargetWarn: false,
      trialWarn: false,
      units: { left: "px", top: "px", rotation: "deg" }
    });

    // Set global defaults for 60fps performance
    gsap.defaults({
      duration: 0.5,
      ease: "power2.out",
      force3D: true,
      lazy: false, // Disable lazy rendering for consistent 60fps
    });

    // Setup performance monitoring
    this.setupPerformanceMonitoring();
    
    // Setup batch animation processing
    this.setupBatchProcessing();

    this.initialized = true;
  }

  // Setup performance monitoring
  private static setupPerformanceMonitoring() {
    let frameCount = 0;
    let lastTime = performance.now();
    let fps = 60;

    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime >= lastTime + 1000) {
        fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        frameCount = 0;
        lastTime = currentTime;
        
        // Adjust animation quality based on FPS
        this.adjustQualityBasedOnFPS(fps);
      }
      
      requestAnimationFrame(measureFPS);
    };

    requestAnimationFrame(measureFPS);
  }

  // Adjust animation quality based on current FPS
  private static adjustQualityBasedOnFPS(fps: number) {
    if (fps < 30) {
      // Low FPS - reduce animation quality
      gsap.globalTimeline.timeScale(0.8);
      console.warn('Low FPS detected, reducing animation quality');
    } else if (fps < 45) {
      // Medium FPS - slightly reduce quality
      gsap.globalTimeline.timeScale(0.9);
    } else {
      // Good FPS - full quality
      gsap.globalTimeline.timeScale(1);
    }
  }

  // Batch animation processing for better performance
  private static setupBatchProcessing() {
    const processBatch = () => {
      if (this.pendingAnimations.length > 0) {
        const animations = this.pendingAnimations.splice(0);
        animations.forEach(animation => animation());
      }
      this.rafId = requestAnimationFrame(processBatch);
    };

    this.rafId = requestAnimationFrame(processBatch);
  }

  // Queue animation for batch processing
  static queueAnimation(animation: () => void) {
    this.pendingAnimations.push(animation);
  }

  // Optimized fade in animation
  static fadeIn(element: HTMLElement | string, options: gsap.TweenVars = {}) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    return gsap.fromTo(target,
      { 
        opacity: 0,
        willChange: 'opacity'
      },
      {
        opacity: 1,
        duration: 0.5,
        ease: "power2.out",
        force3D: true,
        onComplete: () => {
          gsap.set(target, { willChange: 'auto' });
        },
        ...options
      }
    );
  }

  // Optimized slide up animation
  static slideUp(element: HTMLElement | string, options: gsap.TweenVars = {}) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    return gsap.fromTo(target,
      { 
        opacity: 0, 
        y: 50,
        willChange: 'transform, opacity'
      },
      {
        opacity: 1,
        y: 0,
        duration: 0.6,
        ease: "power2.out",
        force3D: true,
        onComplete: () => {
          gsap.set(target, { willChange: 'auto' });
        },
        ...options
      }
    );
  }

  // Optimized scale animation
  static scaleIn(element: HTMLElement | string, options: gsap.TweenVars = {}) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    return gsap.fromTo(target,
      { 
        opacity: 0, 
        scale: 0.8,
        willChange: 'transform, opacity'
      },
      {
        opacity: 1,
        scale: 1,
        duration: 0.5,
        ease: "back.out(1.7)",
        force3D: true,
        onComplete: () => {
          gsap.set(target, { willChange: 'auto' });
        },
        ...options
      }
    );
  }

  // Optimized stagger animation
  static staggerIn(elements: HTMLElement[] | NodeList | string, options: gsap.TweenVars = {}) {
    const targets = typeof elements === 'string' ? document.querySelectorAll(elements) : elements;
    if (!targets || targets.length === 0) return;

    // Set will-change for all elements
    gsap.set(targets, { willChange: 'transform, opacity' });

    return gsap.fromTo(targets,
      { 
        opacity: 0, 
        y: 30 
      },
      {
        opacity: 1,
        y: 0,
        duration: 0.6,
        ease: "power2.out",
        stagger: 0.1,
        force3D: true,
        onComplete: () => {
          gsap.set(targets, { willChange: 'auto' });
        },
        ...options
      }
    );
  }

  // Optimized hover lift effect
  static setupHoverLift(element: HTMLElement | string, options: any = {}) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    let hoverTween: gsap.core.Tween;
    let resetTween: gsap.core.Tween;

    const handleMouseEnter = () => {
      if (resetTween) resetTween.kill();
      gsap.set(target, { willChange: 'transform' });
      
      hoverTween = gsap.to(target, {
        y: -5,
        duration: 0.3,
        ease: "power2.out",
        force3D: true,
        ...options
      });
    };

    const handleMouseLeave = () => {
      if (hoverTween) hoverTween.kill();
      
      resetTween = gsap.to(target, {
        y: 0,
        duration: 0.3,
        ease: "power2.out",
        force3D: true,
        onComplete: () => {
          gsap.set(target, { willChange: 'auto' });
        }
      });
    };

    target.addEventListener('mouseenter', handleMouseEnter);
    target.addEventListener('mouseleave', handleMouseLeave);

    return {
      destroy: () => {
        target.removeEventListener('mouseenter', handleMouseEnter);
        target.removeEventListener('mouseleave', handleMouseLeave);
        if (hoverTween) hoverTween.kill();
        if (resetTween) resetTween.kill();
        gsap.set(target, { willChange: 'auto' });
      }
    };
  }

  // Optimized modal animations
  static modalEnter(element: HTMLElement | string, options: gsap.TweenVars = {}) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    gsap.set(target, { willChange: 'transform, opacity' });

    const tl = gsap.timeline({
      onComplete: () => {
        gsap.set(target, { willChange: 'auto' });
      }
    });

    tl.fromTo(target,
      { 
        opacity: 0, 
        scale: 0.9, 
        y: 20 
      },
      {
        opacity: 1,
        scale: 1,
        y: 0,
        duration: 0.3,
        ease: "power2.out",
        force3D: true,
        ...options
      }
    );

    return tl;
  }

  static modalExit(element: HTMLElement | string, options: gsap.TweenVars = {}) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    return gsap.to(target, {
      opacity: 0,
      scale: 0.9,
      y: -20,
      duration: 0.2,
      ease: "power2.in",
      force3D: true,
      onComplete: () => {
        gsap.set(target, { willChange: 'auto' });
      },
      ...options
    });
  }

  // Cleanup all animations and reset performance settings
  static cleanup() {
    if (this.rafId) {
      cancelAnimationFrame(this.rafId);
      this.rafId = null;
    }
    
    this.pendingAnimations = [];
    gsap.killTweensOf("*");
    gsap.globalTimeline.timeScale(1);
    this.initialized = false;
  }

  // Check if device supports hardware acceleration
  static supportsHardwareAcceleration(): boolean {
    const testElement = document.createElement('div');
    testElement.style.transform = 'translate3d(0,0,0)';
    return testElement.style.transform !== '';
  }

  // Optimize for mobile devices
  static optimizeForMobile() {
    if (typeof window === 'undefined') return;

    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    
    if (isMobile) {
      // Reduce animation quality on mobile
      gsap.defaults({
        duration: 0.3,
        ease: "power1.out"
      });
      
      // Disable complex animations on low-end devices
      if (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4) {
        gsap.globalTimeline.timeScale(0.7);
      }
    }
  }

  // Get current FPS
  static getCurrentFPS(): number {
    return 60; // This would be updated by the performance monitoring
  }
}

// Auto-initialize when DOM is ready
if (typeof window !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      GSAPPerformance.init();
      GSAPPerformance.optimizeForMobile();
    });
  } else {
    GSAPPerformance.init();
    GSAPPerformance.optimizeForMobile();
  }
}

export default GSAPPerformance;
