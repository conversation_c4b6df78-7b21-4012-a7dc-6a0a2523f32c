'use client';

import { gsap } from 'gsap';
import GSAP60FPSOptimizer from './gsap-60fps-optimizer';

// Professional GSAP Component System for React
export class GSAPProfessionalComponents {
  private static initialized = false;
  private static componentAnimations: Map<string, gsap.core.Timeline> = new Map();

  // Initialize professional component system
  static init() {
    if (this.initialized || typeof window === 'undefined') return;

    // Setup automatic component detection
    this.setupComponentDetection();
    
    // Initialize existing components
    this.initializeExistingComponents();

    this.initialized = true;
  }

  // Setup automatic component detection
  private static setupComponentDetection() {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach(mutation => {
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as HTMLElement;
            this.processNewElement(element);
          }
        });
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  // Process new elements for GSAP animations
  private static processNewElement(element: HTMLElement) {
    // Check for GSAP animation classes
    if (element.classList.contains('gsap-fade-in-scroll')) {
      this.setupFadeInScroll(element);
    }
    
    if (element.classList.contains('gsap-slide-in-scroll')) {
      this.setupSlideInScroll(element);
    }
    
    if (element.classList.contains('gsap-stagger-scroll')) {
      this.setupStaggerScroll(element);
    }
    
    if (element.classList.contains('gsap-scale-scroll')) {
      this.setupScaleScroll(element);
    }

    // Process children
    element.querySelectorAll('[class*="gsap-"]').forEach(child => {
      this.processNewElement(child as HTMLElement);
    });
  }

  // Initialize existing components
  private static initializeExistingComponents() {
    // Process all existing GSAP elements
    document.querySelectorAll('[class*="gsap-"]').forEach(element => {
      this.processNewElement(element as HTMLElement);
    });
  }

  // Professional fade in on scroll
  private static setupFadeInScroll(element: HTMLElement) {
    const id = this.generateId(element);
    
    gsap.set(element, { 
      opacity: 0, 
      y: 30,
      willChange: 'transform, opacity'
    });

    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: element,
        start: "top 85%",
        end: "bottom 15%",
        toggleActions: "play none none reverse",
        onComplete: () => {
          gsap.set(element, { willChange: 'auto' });
        }
      }
    });

    tl.to(element, {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: "power2.out",
      force3D: true
    });

    this.componentAnimations.set(id, tl);
  }

  // Professional slide in on scroll
  private static setupSlideInScroll(element: HTMLElement) {
    const id = this.generateId(element);
    const direction = element.dataset.direction || 'up';
    const distance = parseInt(element.dataset.distance) || 50;
    
    let fromProps: any = { opacity: 0 };
    let toProps: any = { opacity: 1 };
    
    switch (direction) {
      case 'up':
        fromProps.y = distance;
        toProps.y = 0;
        break;
      case 'down':
        fromProps.y = -distance;
        toProps.y = 0;
        break;
      case 'left':
        fromProps.x = distance;
        toProps.x = 0;
        break;
      case 'right':
        fromProps.x = -distance;
        toProps.x = 0;
        break;
    }

    gsap.set(element, { 
      ...fromProps,
      willChange: 'transform, opacity'
    });

    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: element,
        start: "top 80%",
        end: "bottom 20%",
        toggleActions: "play none none reverse",
        onComplete: () => {
          gsap.set(element, { willChange: 'auto' });
        }
      }
    });

    tl.to(element, {
      ...toProps,
      duration: 1,
      ease: "power2.out",
      force3D: true
    });

    this.componentAnimations.set(id, tl);
  }

  // Professional stagger on scroll
  private static setupStaggerScroll(element: HTMLElement) {
    const id = this.generateId(element);
    const children = Array.from(element.children) as HTMLElement[];
    const staggerAmount = parseFloat(element.dataset.stagger) || 0.1;
    
    gsap.set(children, { 
      opacity: 0, 
      y: 30,
      willChange: 'transform, opacity'
    });

    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: element,
        start: "top 85%",
        end: "bottom 15%",
        toggleActions: "play none none reverse",
        onComplete: () => {
          gsap.set(children, { willChange: 'auto' });
        }
      }
    });

    tl.to(children, {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: "power2.out",
      force3D: true,
      stagger: staggerAmount
    });

    this.componentAnimations.set(id, tl);
  }

  // Professional scale on scroll
  private static setupScaleScroll(element: HTMLElement) {
    const id = this.generateId(element);
    
    gsap.set(element, { 
      opacity: 0, 
      scale: 0.8,
      willChange: 'transform, opacity'
    });

    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: element,
        start: "top 85%",
        end: "bottom 15%",
        toggleActions: "play none none reverse",
        onComplete: () => {
          gsap.set(element, { willChange: 'auto' });
        }
      }
    });

    tl.to(element, {
      opacity: 1,
      scale: 1,
      duration: 1,
      ease: "back.out(1.7)",
      force3D: true
    });

    this.componentAnimations.set(id, tl);
  }

  // Professional hover effects
  static setupProfessionalHover(element: HTMLElement | string, options: any = {}) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    const hoverProps = {
      y: -5,
      scale: 1.02,
      duration: 0.3,
      ease: "power2.out",
      ...options.hover
    };

    const resetProps = {
      y: 0,
      scale: 1,
      duration: 0.3,
      ease: "power2.out",
      ...options.reset
    };

    return GSAP60FPSOptimizer.hover60FPS(target, hoverProps, resetProps);
  }

  // Professional button animation
  static setupProfessionalButton(element: HTMLElement | string) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    let pressAnimation: gsap.core.Tween;
    let hoverAnimation: gsap.core.Tween;

    const handleMouseEnter = () => {
      if (hoverAnimation) hoverAnimation.kill();
      gsap.set(target, { willChange: 'transform' });
      
      hoverAnimation = gsap.to(target, {
        y: -2,
        scale: 1.02,
        duration: 0.2,
        ease: "power2.out",
        force3D: true
      });
    };

    const handleMouseLeave = () => {
      if (hoverAnimation) hoverAnimation.kill();
      if (pressAnimation) pressAnimation.kill();
      
      gsap.to(target, {
        y: 0,
        scale: 1,
        duration: 0.3,
        ease: "power2.out",
        force3D: true,
        onComplete: () => {
          gsap.set(target, { willChange: 'auto' });
        }
      });
    };

    const handleMouseDown = () => {
      if (pressAnimation) pressAnimation.kill();
      
      pressAnimation = gsap.to(target, {
        scale: 0.98,
        duration: 0.1,
        ease: "power2.out",
        force3D: true
      });
    };

    const handleMouseUp = () => {
      if (pressAnimation) pressAnimation.kill();
      
      gsap.to(target, {
        scale: 1.02,
        duration: 0.2,
        ease: "back.out(1.7)",
        force3D: true
      });
    };

    target.addEventListener('mouseenter', handleMouseEnter);
    target.addEventListener('mouseleave', handleMouseLeave);
    target.addEventListener('mousedown', handleMouseDown);
    target.addEventListener('mouseup', handleMouseUp);

    return {
      destroy: () => {
        target.removeEventListener('mouseenter', handleMouseEnter);
        target.removeEventListener('mouseleave', handleMouseLeave);
        target.removeEventListener('mousedown', handleMouseDown);
        target.removeEventListener('mouseup', handleMouseUp);
        if (hoverAnimation) hoverAnimation.kill();
        if (pressAnimation) pressAnimation.kill();
        gsap.set(target, { willChange: 'auto' });
      }
    };
  }

  // Professional modal animation
  static animateModalIn(element: HTMLElement | string, options: any = {}) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    gsap.set(target, { 
      opacity: 0, 
      scale: 0.9, 
      y: 20,
      willChange: 'transform, opacity'
    });

    return gsap.to(target, {
      opacity: 1,
      scale: 1,
      y: 0,
      duration: 0.4,
      ease: "power2.out",
      force3D: true,
      onComplete: () => {
        gsap.set(target, { willChange: 'auto' });
      },
      ...options
    });
  }

  static animateModalOut(element: HTMLElement | string, options: any = {}) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    return gsap.to(target, {
      opacity: 0,
      scale: 0.9,
      y: -20,
      duration: 0.3,
      ease: "power2.in",
      force3D: true,
      onComplete: () => {
        gsap.set(target, { willChange: 'auto' });
      },
      ...options
    });
  }

  // Generate unique ID for elements
  private static generateId(element: HTMLElement): string {
    return element.id || `gsap-${Math.random().toString(36).substr(2, 9)}`;
  }

  // Cleanup all component animations
  static cleanup() {
    this.componentAnimations.forEach(tl => tl.kill());
    this.componentAnimations.clear();
    this.initialized = false;
  }

  // Refresh all animations (useful for dynamic content)
  static refresh() {
    this.cleanup();
    this.init();
  }
}

// Auto-initialize
if (typeof window !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => GSAPProfessionalComponents.init());
  } else {
    GSAPProfessionalComponents.init();
  }
}

export default GSAPProfessionalComponents;
