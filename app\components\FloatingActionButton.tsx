'use client'

import { useState, useEffect, useRef } from 'react'
import Link from 'next/link'
import { gsap } from 'gsap'
import { Calendar, Phone } from 'lucide-react'

const FloatingActionButton = () => {
  const [isVisible, setIsVisible] = useState(false)
  const [isHovered, setIsHovered] = useState(false)
  const buttonRef = useRef<HTMLDivElement>(null)
  const linkRef = useRef<HTMLAnchorElement>(null)

  useEffect(() => {
    // Show the button after a delay with GSAP animation
    const timer = setTimeout(() => {
      setIsVisible(true)

      if (buttonRef.current) {
        gsap.fromTo(buttonRef.current,
          { opacity: 0, y: 20, scale: 0.8 },
          {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: 0.5,
            ease: "back.out(1.7)",
            force3D: true
          }
        )
      }
    }, 2000)

    return () => clearTimeout(timer)
  }, [])

  // Handle hover animations with GSAP
  const handleMouseEnter = () => {
    setIsHovered(true)
    if (linkRef.current) {
      gsap.to(linkRef.current, {
        y: -2,
        duration: 0.3,
        ease: "power2.out",
        force3D: true
      })
    }
  }

  const handleMouseLeave = () => {
    setIsHovered(false)
    if (linkRef.current) {
      gsap.to(linkRef.current, {
        y: 0,
        duration: 0.3,
        ease: "power2.out",
        force3D: true
      })
    }
  }

  return (
    <>
      {isVisible && (
        <div
          ref={buttonRef}
          className="relative z-50"
        >
          <Link
            ref={linkRef}
            href="/discovery-call"
            className="group flex items-center gap-1 px-3 py-2 bg-gradient-to-r from-violet-600 to-purple-600
                     hover:from-violet-500 hover:to-purple-500 text-white rounded-full shadow-lg
                     hover:shadow-purple-500/25"
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
          >
            <Phone className="w-4 h-4 text-purple-200 flex-shrink-0" />
            <span className={`text-sm font-medium tracking-wide whitespace-nowrap overflow-hidden transition-all duration-300 ${
              isHovered ? 'max-w-[150px] opacity-100 ml-1' : 'max-w-0 opacity-0 ml-0'
            }`}>
              Book Call
            </span>
            <Calendar className={`w-4 h-4 flex-shrink-0 transition-all duration-300 ${
              isHovered ? 'opacity-100 ml-1' : 'opacity-0 w-0 ml-0'
            }`} />
          </Link>
        </div>
      )}
    </>
  )
}

export default FloatingActionButton