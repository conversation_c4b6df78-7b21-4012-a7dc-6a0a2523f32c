'use client';

import { gsap } from 'gsap';

// GSAP utility functions for common animations
export class GSAPUtils {
  // Fade animations
  static fadeIn(element: HTMLElement | string, options: gsap.TweenVars = {}) {
    return gsap.fromTo(element, 
      { opacity: 0 },
      { 
        opacity: 1, 
        duration: 0.5,
        ease: "power2.out",
        force3D: true,
        ...options 
      }
    );
  }

  static fadeOut(element: HTMLElement | string, options: gsap.TweenVars = {}) {
    return gsap.to(element, { 
      opacity: 0, 
      duration: 0.3,
      ease: "power2.out",
      force3D: true,
      ...options 
    });
  }

  // Slide animations
  static slideUp(element: HTMLElement | string, options: gsap.TweenVars = {}) {
    return gsap.fromTo(element,
      { opacity: 0, y: 50 },
      { 
        opacity: 1, 
        y: 0, 
        duration: 0.6,
        ease: "power2.out",
        force3D: true,
        ...options 
      }
    );
  }

  static slideDown(element: HTMLElement | string, options: gsap.TweenVars = {}) {
    return gsap.fromTo(element,
      { opacity: 0, y: -50 },
      { 
        opacity: 1, 
        y: 0, 
        duration: 0.6,
        ease: "power2.out",
        force3D: true,
        ...options 
      }
    );
  }

  static slideLeft(element: HTMLElement | string, options: gsap.TweenVars = {}) {
    return gsap.fromTo(element,
      { opacity: 0, x: 50 },
      { 
        opacity: 1, 
        x: 0, 
        duration: 0.6,
        ease: "power2.out",
        force3D: true,
        ...options 
      }
    );
  }

  static slideRight(element: HTMLElement | string, options: gsap.TweenVars = {}) {
    return gsap.fromTo(element,
      { opacity: 0, x: -50 },
      { 
        opacity: 1, 
        x: 0, 
        duration: 0.6,
        ease: "power2.out",
        force3D: true,
        ...options 
      }
    );
  }

  // Scale animations
  static scaleIn(element: HTMLElement | string, options: gsap.TweenVars = {}) {
    return gsap.fromTo(element,
      { opacity: 0, scale: 0.8 },
      { 
        opacity: 1, 
        scale: 1, 
        duration: 0.5,
        ease: "back.out(1.7)",
        force3D: true,
        ...options 
      }
    );
  }

  static scaleOut(element: HTMLElement | string, options: gsap.TweenVars = {}) {
    return gsap.to(element, { 
      opacity: 0, 
      scale: 0.8, 
      duration: 0.3,
      ease: "power2.in",
      force3D: true,
      ...options 
    });
  }

  // Float animation (replaces CSS keyframes)
  static float(element: HTMLElement | string, options: gsap.TweenVars = {}) {
    return gsap.to(element, {
      y: -10,
      duration: 2,
      ease: "power1.inOut",
      yoyo: true,
      repeat: -1,
      force3D: true,
      ...options
    });
  }

  // Pulse animation
  static pulse(element: HTMLElement | string, options: gsap.TweenVars = {}) {
    return gsap.to(element, {
      scale: 1.05,
      duration: 1,
      ease: "power1.inOut",
      yoyo: true,
      repeat: -1,
      force3D: true,
      ...options
    });
  }

  // Glow pulse animation
  static glowPulse(element: HTMLElement | string, options: gsap.TweenVars = {}) {
    return gsap.to(element, {
      boxShadow: "0 0 20px 5px rgba(147, 51, 234, 0.7)",
      duration: 2,
      ease: "power1.inOut",
      yoyo: true,
      repeat: -1,
      ...options
    });
  }

  // Stagger animations for multiple elements
  static staggerFadeIn(elements: HTMLElement[] | string, options: gsap.TweenVars = {}) {
    return gsap.fromTo(elements,
      { opacity: 0, y: 30 },
      { 
        opacity: 1, 
        y: 0, 
        duration: 0.6,
        ease: "power2.out",
        stagger: 0.1,
        force3D: true,
        ...options 
      }
    );
  }

  // Modal animations
  static modalEnter(element: HTMLElement | string, options: gsap.TweenVars = {}) {
    const tl = gsap.timeline();
    tl.fromTo(element,
      { opacity: 0, scale: 0.9, y: 20 },
      { 
        opacity: 1, 
        scale: 1, 
        y: 0,
        duration: 0.3,
        ease: "power2.out",
        force3D: true,
        ...options 
      }
    );
    return tl;
  }

  static modalExit(element: HTMLElement | string, options: gsap.TweenVars = {}) {
    return gsap.to(element, { 
      opacity: 0, 
      scale: 0.9, 
      y: -20,
      duration: 0.2,
      ease: "power2.in",
      force3D: true,
      ...options 
    });
  }

  // Page transition animations
  static pageTransitionIn(element: HTMLElement | string, options: gsap.TweenVars = {}) {
    return gsap.fromTo(element,
      { opacity: 0, y: 100 },
      { 
        opacity: 1, 
        y: 0, 
        duration: 0.8,
        ease: "power3.out",
        force3D: true,
        ...options 
      }
    );
  }

  static pageTransitionOut(element: HTMLElement | string, options: gsap.TweenVars = {}) {
    return gsap.to(element, { 
      opacity: 0, 
      y: -100, 
      duration: 0.5,
      ease: "power3.in",
      force3D: true,
      ...options 
    });
  }

  // Hover effects
  static hoverLift(element: HTMLElement | string, options: gsap.TweenVars = {}) {
    return gsap.to(element, { 
      y: -5, 
      duration: 0.3,
      ease: "power2.out",
      force3D: true,
      ...options 
    });
  }

  static hoverReset(element: HTMLElement | string, options: gsap.TweenVars = {}) {
    return gsap.to(element, { 
      y: 0, 
      duration: 0.3,
      ease: "power2.out",
      force3D: true,
      ...options 
    });
  }

  // Utility to kill all animations on an element
  static killAnimations(element: HTMLElement | string) {
    gsap.killTweensOf(element);
  }

  // Set up performance optimizations for 60fps
  static setupPerformance() {
    // Enable hardware acceleration globally
    gsap.config({
      force3D: true,
      nullTargetWarn: false,
      autoSleep: 60, // Automatically pause animations after 60 seconds of inactivity
    });

    // Set up GSAP ticker for consistent 60fps
    gsap.ticker.fps(60);

    // Use RAF for smooth animations
    gsap.ticker.useRAF(true);

    // Optimize for mobile performance
    if (typeof window !== 'undefined') {
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      if (isMobile) {
        // Reduce animation complexity on mobile
        gsap.config({
          force3D: true,
          autoSleep: 30,
        });
      }
    }
  }

  // Check for reduced motion preference
  static respectsReducedMotion(): boolean {
    return typeof window !== 'undefined' && 
           window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  }

  // Create a timeline with reduced motion support
  static createTimeline(options: gsap.TimelineVars = {}) {
    const tl = gsap.timeline(options);
    
    if (this.respectsReducedMotion()) {
      tl.duration(0);
    }
    
    return tl;
  }
}

// React hook for GSAP animations
export function useGSAP() {
  return {
    fadeIn: GSAPUtils.fadeIn,
    fadeOut: GSAPUtils.fadeOut,
    slideUp: GSAPUtils.slideUp,
    slideDown: GSAPUtils.slideDown,
    slideLeft: GSAPUtils.slideLeft,
    slideRight: GSAPUtils.slideRight,
    scaleIn: GSAPUtils.scaleIn,
    scaleOut: GSAPUtils.scaleOut,
    float: GSAPUtils.float,
    pulse: GSAPUtils.pulse,
    glowPulse: GSAPUtils.glowPulse,
    staggerFadeIn: GSAPUtils.staggerFadeIn,
    modalEnter: GSAPUtils.modalEnter,
    modalExit: GSAPUtils.modalExit,
    pageTransitionIn: GSAPUtils.pageTransitionIn,
    pageTransitionOut: GSAPUtils.pageTransitionOut,
    hoverLift: GSAPUtils.hoverLift,
    hoverReset: GSAPUtils.hoverReset,
    killAnimations: GSAPUtils.killAnimations,
    createTimeline: GSAPUtils.createTimeline,
  };
}

// Initialize GSAP performance settings
if (typeof window !== 'undefined') {
  GSAPUtils.setupPerformance();
}
