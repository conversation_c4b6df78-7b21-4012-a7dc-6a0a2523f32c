{"name": "portflio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "postbuild": "node scripts/copy-db-vercel.js", "start": "next start", "lint": "next lint", "db:init": "node -e \"require('./lib/neon').initDatabase().then(console.log)\"", "db:test": "node --experimental-modules scripts/test-neon-connection.js", "update-db-schema": "node scripts/update-database-schema.js", "fix-db": "node scripts/fix-database.js"}, "dependencies": {"@clerk/nextjs": "^6.22.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@mui/system": "^7.1.0", "@neondatabase/serverless": "^1.0.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-slot": "^1.2.0", "@react-three/drei": "^8.15.1", "@react-three/fiber": "^8.15.16", "@shadcn/ui": "^0.0.4", "@tailwindcss/line-clamp": "^0.4.4", "@tanstack/react-virtual": "^3.13.6", "@types/better-sqlite3": "^7.6.13", "@types/gsap": "^1.20.2", "@types/recharts": "^1.8.29", "@vercel/analytics": "^1.5.0", "axios": "^1.6.7", "better-sqlite3": "^11.9.1", "canvas-confetti": "^1.9.3", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "critters": "^0.0.23", "dotenv": "^16.5.0", "framer-motion": "^12.18.1", "gsap": "^3.13.0", "lemonsqueezy.ts": "^0.1.8", "lucide-react": "^0.475.0", "mongoose": "^8.13.1", "next": "^15.2.0", "next-themes": "^0.4.4", "node-fetch": "^3.3.2", "nodemailer": "^6.10.0", "postcss": "^8.4.33", "prismjs": "^1.29.0", "react": "18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "18.3.1", "react-hot-toast": "^2.5.2", "react-icon-cloud": "^4.1.7", "react-icons": "^5.0.1", "react-intersection-observer": "^9.16.0", "react-responsive": "^10.0.0", "react-simple-typewriter": "^5.0.1", "recharts": "^2.15.3", "sqlite": "^5.1.1", "sqlite3": "^5.1.7", "styled-jsx": "^5.1.6", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "three": "^0.175.0", "uuid": "^11.1.0", "zustand": "^5.0.4"}, "devDependencies": {"@tailwindcss/typography": "^0.5.10", "@types/canvas-confetti": "^1.9.0", "@types/lodash": "^4.17.17", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/prismjs": "^1.26.5", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "fs-extra": "^11.3.0", "postcss": "^8.5.3", "sharp": "^0.33.5", "tailwind-scrollbar": "^3.0.5", "tailwindcss": "^3.4.17", "typescript": "^5"}}