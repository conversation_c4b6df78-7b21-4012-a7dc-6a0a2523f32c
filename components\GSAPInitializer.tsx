'use client';

import { useEffect } from 'react';
import GSAPAnimations from '@/lib/gsap-animations';
import GSAPHoverEffects from '@/lib/gsap-hover-effects';
import GSAPPerformance from '@/lib/gsap-performance';
import GSAP60FPSOptimizer from '@/lib/gsap-60fps-optimizer';
import GSAPChatbotProtection from '@/lib/gsap-chatbot-protection';
import GSAPScrollOptimizer from '@/lib/gsap-scroll-optimizer';
import GSAPProfessionalComponents from '@/lib/gsap-professional-components';
import GSAPMemoryOptimizer from '@/lib/gsap-memory-optimizer';

export default function GSAPInitializer() {
  useEffect(() => {
    // Initialize chatbot protection first
    GSAPChatbotProtection.init();

    // Initialize GSAP with professional optimizations
    GSAPMemoryOptimizer.init();
    GSAP60FPSOptimizer.init();
    GSAPPerformance.init();
    GSAPScrollOptimizer.init();
    GSAPProfessionalComponents.init();
    GSAPAnimations.init();
    GSAPHoverEffects.autoSetup();

    // Refresh animations when new content is added dynamically
    const observer = new MutationObserver(() => {
      // Debounce the refresh to avoid excessive calls
      setTimeout(() => {
        GSAPAnimations.refresh();
        GSAPHoverEffects.autoSetup();
      }, 100);
    });

    // Observe changes to the document body
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class']
    });

    // Cleanup
    return () => {
      observer.disconnect();
      GSAPAnimations.cleanup();
      GSAPPerformance.cleanup();
      GSAP60FPSOptimizer.cleanup();
    };
  }, []);

  // This component doesn't render anything
  return null;
}
