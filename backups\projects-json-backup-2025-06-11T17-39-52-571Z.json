[{"id": 1, "title": "NEX-WEBS Tools", "description": "A comprehensive suite of web tools including XML Sitemap Generator, Image Compressor, and SEO tools.(just need an API key for some tools to work)", "detailedDescription": "NEX-WEBS Tools is a powerful collection of utilities designed to help developers and businesses optimize their web presence. The suite includes tools for SEO analysis, performance optimization, and content management. With features like XML sitemap generation, image compression, and metadata analysis, users can significantly improve their website's performance and visibility.", "image": "/projects/9289c8e1-3f27-48e0-afaf-1ad087bce119.png", "category": "Web Development", "technologies": ["Next.js", "Tailwind CSS", "TypeScript"], "techDetails": {"frontend": "Next.js with TypeScript for type safety", "styling": "Tailwind CSS for responsive design", "performance": "Optimized with code splitting and lazy loading"}, "link": "https://1-project-nex-webs.netlify.app/", "featured": true, "completionDate": "2023-10-15", "clientName": "Internal Project", "duration": "8 weeks", "imagePriority": 1, "exclusiveFeatures": [], "visualEffects": {"morphTransition": false, "rippleEffect": false, "floatingElements": false, "shimmering": false, "animation": "none", "shadows": "soft", "border": "solid", "glassmorphism": false, "particles": false, "animationTiming": "normal", "animationIntensity": "normal"}}, {"title": "NEWLY ADDED: YT-VEDIO-ANALYZER", "description": "ANALYZER", "image": "/projects/0c3e9b24-ffb3-4f2f-8afe-00ccdbbd05a9.png", "category": "Web Development", "technologies": ["AI MODEL FOR RESEARCH"], "link": "https://nex-webs-project-6.netlify.app/", "featured": true, "developmentProgress": 0, "estimatedCompletion": "", "exclusiveFeatures": ["PREMIUM CONTENT ", "Behind-the-scenes development insights"], "visualEffects": {"morphTransition": true, "rippleEffect": true, "floatingElements": false, "shimmering": false, "animation": "elastic", "shadows": "neon", "border": "double", "glassmorphism": false, "particles": true, "animationTiming": "very-slow", "animationIntensity": "subtle"}, "imagePriority": 1, "status": "In Development", "updatedDays": 1, "progress": "48", "id": 14}, {"id": 2, "title": "NEXTJS-WEBSITE", "description": "A creative portfolio website with interactive elements and smooth animations, showcasing various projects and skills.", "detailedDescription": "This portfolio website demonstrates advanced Next.js capabilities with a focus on user experience and modern design principles. The site features custom animations, interactive components, and optimized performance metrics. The responsive design ensures a consistent experience across all devices.", "image": "/projects/8836a47d-e499-472e-bce5-e5c9361b6a7c.png", "category": "Web Development", "technologies": ["React", "Framer Motion", "Tailwind CSS", "TypeScript"], "techDetails": {"frontend": "React with TypeScript for scalable components", "animations": "Framer Motion for smooth transitions and effects", "styling": "Tailwind CSS for utility-first styling"}, "link": "https://nex-webs-project2.netlify.app/", "featured": true, "completionDate": "2023-12-05", "clientName": "Self-branded", "duration": "6 weeks", "exclusiveFeatures": [], "imagePriority": 1, "visualEffects": {"morphTransition": false, "rippleEffect": false, "floatingElements": false, "shimmering": false, "animation": "bounce", "shadows": "neon", "border": "animated", "glassmorphism": true, "particles": true, "animationTiming": "normal", "animationIntensity": "normal"}}, {"title": "NEWLY ADDED: PROJECT ARA BY (NEX-DEVS)", "description": "YOULL BE AMAZED..!!", "image": "/projects/c0e3e4a3-58ba-4e27-a61e-7dad197af43d.png", "category": "Web Development", "technologies": ["GOOGLE DEEP THINK "], "link": "https://3d-portfolio-showcase.vercel.app", "featured": true, "developmentProgress": 0, "estimatedCompletion": "", "exclusiveFeatures": ["Experimental features not available in public release", "Limited edition design elements", "Special promotions for early adopters"], "visualEffects": {"glow": false, "animation": "float", "showBadge": true, "spotlight": true, "shadows": "soft", "border": "dashed"}, "imagePriority": 1, "status": "In Development", "updatedDays": 1, "progress": "59", "id": 16}, {"title": "NEWLY ADDED: PET-GPT (By NEX-DEVS)", "description": "a vercitile PET query related chat bot", "image": "/projects/1aef2df5-ec63-42c1-9c05-f37a1a4d152e.png", "category": "Web Development with AI Integration", "technologies": ["WEB SEARCH "], "link": "https://3d-portfolio-showcase.vercel.app", "featured": true, "status": "Recently Launched", "updatedDays": 1, "progress": "75", "features": ["DEEP SEARCH "], "id": 13, "imagePriority": 1, "visualEffects": {"morphTransition": true, "rippleEffect": false, "floatingElements": true, "shimmering": false, "animation": "pop", "shadows": "3d", "border": "animated", "glassmorphism": true, "particles": true, "animationTiming": "very-slow", "animationIntensity": "strong"}, "exclusiveFeatures": ["Early access to premium content", "Experimental features not available in public release", "Limited edition design elements"], "secondImage": "/projects/2859ce8c-65c3-4780-894a-7434a0cf7a78.png", "showBothImagesInPriority": true}, {"id": 11, "title": "NEWLY ADDED: 3D Portfolio Website", "description": "A 3D website showcasing immersive experiences with interactive elements and stunning animations.", "detailedDescription": "This cutting-edge 3D portfolio website pushes the boundaries of web technology to create an immersive digital experience. It features interactive 3D models that respond to user input, custom shader effects that create unique visual atmospheres, and a seamless navigation system that guides visitors through different project environments. Each project is presented in its own dedicated space with contextual information and interaction points.", "image": "/projects/3d-portfolio.jpg", "category": "UI/UX Design", "technologies": ["Three.js", "React Three Fiber", "GSAP", "WebGL"], "techDetails": {"3D": "Three.js with custom WebGL shaders for visual effects", "framework": "React Three Fiber for component-based 3D development", "animation": "GSAP for timeline-based animations and transitions", "performance": "Optimized asset loading and level-of-detail rendering"}, "link": "https://3d-portfolio-showcase.vercel.app", "featured": true, "status": "In Development", "updatedDays": 2, "progress": 99, "features": ["Interactive 3D models and animations", "Custom shader effects", "Responsive 3D environments"], "completionDate": "Expected June 2024", "clientName": "Personal Portfolio", "duration": "3 months"}, {"id": 12, "title": "NEWLY ADDED: Fullstack Dashboard", "description": "A complete dashboard solution with authentication, analytics, and real-time data visualization.", "detailedDescription": "The Fullstack Dashboard is a comprehensive solution for business intelligence and data management. It provides a secure authentication system with role-based access control, real-time analytics that process and visualize data streams, and customizable reporting tools. The dashboard features a modular architecture that allows for easy extension and integration with existing business systems.", "image": "/projects/dashboard.jpg", "category": "Web Development", "technologies": ["Next.js 14", "Tailwind CSS", "Prisma", "MongoDB"], "techDetails": {"frontend": "Next.js 14 with App Router and React Server Components", "database": "MongoDB with Prisma ORM for type-safe queries", "styling": "Tailwind CSS with custom design system", "auth": "NextAuth.js with OAuth 2.0 and role-based permissions"}, "link": "https://fullstack-dashboard.vercel.app", "featured": true, "status": "Beta Testing", "updatedDays": 5, "progress": 50, "features": ["OAuth 2.0 Authentication", "Real-time Analytics", "Data Visualization"], "completionDate": "Expected July 2024", "clientName": "Enterprise SaaS Platform", "duration": "4 months"}, {"id": 6, "title": "CPU & GPU Bottleneck Calculator(AI-Agent)", "description": "A modern and intuitive NFT marketplace design with dark theme and glass-morphism effects.", "detailedDescription": "The CPU & GPU Bottleneck Calculator is an advanced tool that helps users identify performance bottlenecks in their computer systems. Using AI-driven analysis, it compares CPU and GPU specifications, evaluates their compatibility, and provides detailed recommendations for optimizing performance. The tool includes visual representations of bottlenecks and personalized upgrade suggestions.", "image": "/projects/1a58fe2c-191f-4164-8527-728b4f518c5f.png", "category": "UI/UX Design", "technologies": ["Figma", "<PERSON><PERSON>der", "After Effects"], "techDetails": {"design": "Figma for UI/UX with custom component library", "3D": "Blender for hardware visualization models", "animation": "After Effects for performance demonstration animations", "data": "Extensive hardware database with performance metrics"}, "link": "https://project-4-updated.vercel.app/", "featured": true, "completionDate": "2023-11-30", "clientName": "Hardware Benchmark Platform", "duration": "12 weeks"}, {"id": 4, "title": "WEB-APP-(Gratuity Calculator 2025)", "description": "This tool simplifies the complex process of gratuity calculation, ensuring you have a clear understanding of your entitlements.", "detailedDescription": "The Gratuity Calculator 2025 is a comprehensive tool that factors in the latest regulations and policies to provide accurate gratuity estimations. It includes features for different employment scenarios, various country regulations, and detailed breakdowns of calculations. The tool also provides downloadable reports and historical calculation tracking.", "image": "/projects/1bba4cac-58bb-4fa0-9cf1-c7062bb94629.png", "category": "Web Development", "technologies": ["React", "TensorFlow.js", "Node.js"], "techDetails": {"frontend": "React with responsive forms and visualization", "backend": "Node.js for calculation logic and data processing", "ai": "TensorFlow.js for predictive analysis of financial trends"}, "link": "https://nex-webs-project-9.netlify.app/", "featured": true, "completionDate": "2024-01-10", "clientName": "Financial Services Company", "duration": "10 weeks"}, {"id": 5, "title": "Invisible Character Generator(Web-App)", "description": "This tool is designed to help you generate and copy invisible characters easily.", "detailedDescription": "The Invisible Character Generator is a specialized utility for digital content creators and developers. It generates various types of invisible Unicode characters that can be used for spacing, formatting, and special effects in digital content. The tool includes a preview feature to test the characters in different contexts and a quick-copy functionality for easy integration.", "image": "/projects/4f615362-0f11-4cc2-baa3-b708c837be03.png", "category": "Web Development", "technologies": ["Vue.js", "Firebase", "TailwindCSS", "Python"], "techDetails": {"frontend": "Vue.js for reactive interface and state management", "backend": "Python scripts for character generation algorithms", "database": "Firebase for user preferences and history", "styling": "TailwindCSS for clean, utility-first design"}, "link": "https://nex-webs-project-4.netlify.app/", "featured": false, "completionDate": "2023-07-15", "clientName": "Open Source Initiative", "duration": "5 weeks"}, {"id": 3, "title": "Morse Code Translator(Web-App)", "description": "Express yourself with unique Morse Code products—perfect for personalized gifts, home decor, and more. Speak in dots and dashes today!", "detailedDescription": "The Morse Code Translator is an interactive web application that converts text to Morse code and vice versa. It includes audio playback of the Morse code, visual representations, and customizable speed settings. The application also includes a learning mode for users unfamiliar with Morse code.", "image": "/projects/806ed4b7-10b2-49bb-b8b3-c13a3ec6d597.png", "category": "UI/UX Design", "technologies": ["Figma", "Adobe XD", "Prototyping"], "techDetails": {"design": "Figma and Adobe XD for high-fidelity mockups", "prototyping": "Interactive prototypes with animation", "research": "User testing and feedback implementation"}, "link": "https://nex-webs-project-6.netlify.app/", "featured": false, "completionDate": "2023-08-20", "clientName": "Educational Project", "duration": "4 weeks"}]