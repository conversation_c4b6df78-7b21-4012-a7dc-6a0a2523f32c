/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: [], // Add any image domains you need
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 0, // Reduce cache TTL for better freshness
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048],
    imageSizes: [16, 32, 48, 64, 96, 128, 256],
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    unoptimized: true, // Allow unoptimized images for data URLs
  },
  typescript: {
    ignoreBuildErrors: true, // Temporarily ignore TS errors during build
  },
  eslint: {
    ignoreDuringBuilds: true, // Temporarily ignore ESLint errors during build
  },
  poweredByHeader: false,
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error', 'warn', 'info'], // Keep important logs
    } : false,
  },
  experimental: {
    optimizeCss: true,
    scrollRestoration: true,
    optimizePackageImports: ['framer-motion', 'react-icons'],
    largePageDataBytes: 256 * 1000, // Increase limit for large responses
  },
  // output: 'standalone', // Commented out as it might conflict with Vercel deployment
  env: {
    EMAIL_PASSWORD: process.env.EMAIL_PASSWORD,
    DISABLE_CACHE: 'true', // Force disable cache
  },
  // Server configuration
  serverRuntimeConfig: {
    // Will only be available on the server side
    EMAIL_PASSWORD: process.env.EMAIL_PASSWORD,
    PROJECT_CACHE_DISABLED: 'true', // Disable project caching on server
    NODE_ENV: process.env.NODE_ENV || 'development',
  },
  publicRuntimeConfig: {
    // Will be available on both server and client
    NODE_ENV: process.env.NODE_ENV,
    BUILD_ID: Date.now().toString(), // Add unique build ID to force cache refreshes
    CACHE_DISABLED: 'true',
  },
  // Add custom headers to prevent caching of API responses
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          { key: 'Cache-Control', value: 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0' },
          { key: 'Pragma', value: 'no-cache' },
          { key: 'Expires', value: '0' },
          { key: 'Surrogate-Control', value: 'no-store' },
          { key: 'X-Content-Type-Options', value: 'nosniff' }
        ],
      },
      {
        source: '/admin/:path*',
        headers: [
          { key: 'Cache-Control', value: 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0' },
          { key: 'Pragma', value: 'no-cache' },
          { key: 'Expires', value: '0' },
        ],
      },
    ];
  },
  // Force Vercel to rebuild the project on every deployment 
  generateBuildId: () => {
    return `build-${Date.now()}`;
  },
}

module.exports = nextConfig 