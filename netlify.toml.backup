[build]
  command = "npm run build"
  publish = ".next"

# Use the Next.js Runtime
[[plugins]]
  package = "@netlify/plugin-nextjs"

# Copy the database file during build using our local plugin
[[plugins]]
  package = "./netlify/plugins/copy-files.js"
  
  [plugins.inputs]
    source = "db/portfolio.db"
    destination = ".next/server/db/portfolio.db"

# Environment variables
[build.environment]
  NODE_ENV = "production"
  NEXT_TELEMETRY_DISABLED = "1"

# Cache control headers
[[headers]]
  for = "/api/*"
  [headers.values]
    Cache-Control = "no-cache, no-store, must-revalidate"
    Pragma = "no-cache"
    Expires = "0"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "accelerometer=(), camera=(), geolocation=(), microphone=()" 