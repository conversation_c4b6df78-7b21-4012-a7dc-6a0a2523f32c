'use client'

import { motion, useScroll, useTransform } from 'framer-motion'
import Link from 'next/link'
import Image from 'next/image'
import { useState, useEffect, useRef } from 'react'
import { Button } from '@/components/ui/button'
import NexShiftFlowchart from './NexShiftFlowchart'
import NeuralNetwork from '../animations/NeuralNetwork'
import FastMovingLines from '../animations/FastMovingLines'



// Business benefits for showcase
const businessBenefits = [
  {
    title: "Grow Revenue",
    description: "Increase your conversion rate and revenue with our proven tactics",
    icon: "📈"
  },
  {
    title: "Expand Reach",
    description: "Reach new customers through optimized digital channels",
    icon: "🌐"
  },
  {
    title: "Save Time",
    description: "Automate processes and focus on what matters most for your business",
    icon: "⏱️"
  },
  {
    title: "Scale Efficiently",
    description: "Our solutions grow with your business needs",
    icon: "🚀"
  }
]

// Advanced technology features
const advancedFeatures = [
  {
    title: "AI Integration",
    description: "Custom AI solutions that adapt to your business needs",
    icon: "🧠",
    details: ["Chatbot Integration", "Smart Content Generation", "User Behavior Analysis"]
  },
  {
    title: "3D Experiences",
    description: "Immersive 3D elements that engage users and improve brand perception",
    icon: "🌐",
    details: ["Three.js Scenes", "3D Product Showcases", "Interactive Elements"]
  },
  {
    title: "Serverless Architecture",
    description: "Modern cloud solutions for scalability and performance",
    icon: "☁️",
    details: ["AWS/Azure/GCP", "Microservices", "Edge Computing"]
  },
  {
    title: "Mobile-First Design",
    description: "Responsive solutions that prioritize mobile experiences",
    icon: "📱",
    details: ["React Native", "PWA Support", "Cross-platform Compatibility"]
  }
]

// Core technologies
const coreTechnologies = [
  { name: "React + Next.js", level: 99, color: "bg-blue-500" },
  { name: "Node.js & Express", level: 95, color: "bg-green-500" },
  { name: "TypeScript", level: 97, color: "bg-blue-400" },
  { name: "Three.js & WebGL", level: 90, color: "bg-purple-500" },
  { name: "TensorFlow & AI", level: 85, color: "bg-orange-500" },
  { name: "AWS Cloud Suite", level: 88, color: "bg-yellow-500" }
]

interface CounterAnimationProps {
  end: number;
  label: string;
  duration?: number;
  prefix?: string;
  suffix?: string;
}

// Counter animation for metrics
const CounterAnimation = ({ end, label, duration = 2, prefix = '', suffix = '' }: CounterAnimationProps) => {
  const [count, setCount] = useState(0)
  
  useEffect(() => {
    let startTime: number | undefined;
    let frameId: number;
    
    const step = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / (duration * 1000), 1);
      setCount(Math.floor(progress * end));
      
      if (progress < 1) {
        frameId = window.requestAnimationFrame(step);
      }
    };
    
    frameId = window.requestAnimationFrame(step);
    
    return () => {
      if (frameId) {
        window.cancelAnimationFrame(frameId);
      }
    };
  }, [end, duration]);

  return (
    <div className="text-center">
      <div className="text-3xl sm:text-4xl font-bold text-white tracking-tight">
        {prefix}{count}{suffix}
      </div>
      <div className="text-sm text-gray-400 mt-1">{label}</div>
    </div>
  )
}

export default function BusinessHero() {
  const containerRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  });
  


  return (
    <motion.section
      ref={containerRef}
      initial="hidden"
      animate="visible"
      variants={{
        hidden: { opacity: 0 },
        visible: { opacity: 1 }
      }}
      transition={{ duration: 0.5 }}
      className="relative min-h-screen flex flex-col justify-center py-8 sm:py-12 px-4 sm:px-6 mt-6 sm:mt-8 will-change-transform overflow-hidden"
      style={{
        transform: 'translate3d(0, 0, 0)',
        background: '#000000 !important',
        backgroundColor: '#000000 !important'
      }}
    >
      {/* Forcefully Black Background */}
      <div className="fixed top-0 left-0 right-0 bottom-0 z-[-4]"
        style={{
          background: '#000000 !important',
          backgroundColor: '#000000 !important',
          transform: 'translate3d(0, 0, 0)'
        }}>
      </div>

      {/* Neural Network Animation Background - More Visible */}
      <div className="fixed inset-0 z-[-2] pointer-events-none">
        <NeuralNetwork
          color="#6366f1"
          lineColor="#4f46e5"
          pointCount={60}
          connectionRadius={180}
          speed={1.2}
        />
      </div>

      {/* Mobile-specific background blur for better text readability */}
      <div className="absolute inset-0 bg-black/20 backdrop-blur-[1px] z-[-1] sm:hidden"
           style={{ transform: 'translate3d(0, 0, 0)' }}></div>

      {/* Reduced empty space for HeroToggle */}
      <div className="h-12 sm:h-14"></div>

      <div className="max-w-7xl mx-auto px-3 sm:px-6 w-full relative z-10">
        {/* Main hero section */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-4 sm:gap-8 items-center relative">
          {/* Left content - enhanced copy */}
          <div className="lg:col-span-7 space-y-5 sm:space-y-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="space-y-4 sm:space-y-6"
            >
              <div className="inline-block px-3 py-1 rounded-full bg-gradient-to-r from-purple-900/30 to-purple-800/30 border border-purple-700/30 text-purple-300 text-xs sm:text-sm">
                <span className="mr-2">✨</span>NEX-DEVS Technology Solutions
              </div>
              
              <h1 className="text-3xl sm:text-5xl md:text-6xl font-bold text-white leading-tight">
                Grow Your <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-indigo-500">Business</span> With Data-Driven Solutions
              </h1>
              
              <p className="text-lg sm:text-xl text-gray-300 max-w-3xl">
                We help transform your web traffic into paying customers with our proven NEX-SHFT mechanism, delivering up to <span className="text-transparent bg-clip-text bg-gradient-to-br from-purple-400 to-indigo-400 font-semibold">55% higher conversion rates</span>.
              </p>
              
              {/* Key points with icons - simplified */}
              <div className="grid grid-cols-2 sm:grid-cols-2 gap-3 sm:gap-4 pt-2 sm:pt-4">
                {[
                  { text: "AI-Powered Solutions", icon: "🧠" },
                  { text: "Modern Web Experiences", icon: "🌐" },
                  { text: "Mobile-First Development", icon: "📱" },
                  { text: "Enterprise-Grade Security", icon: "🔒" }
                ].map((point, i) => (
                  <motion.div 
                    key={i}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3 + (i * 0.1) }}
                    className="flex items-center gap-2 sm:gap-3"
                  >
                    <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-purple-900/20 flex items-center justify-center">
                      <span>{point.icon}</span>
                    </div>
                    <span className="text-gray-300 text-xs sm:text-sm">{point.text}</span>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="flex flex-wrap gap-3 sm:gap-4 pt-2"
            >
              <Link href="/discovery-call">
                <Button className="bg-purple-700 hover:bg-purple-800 text-white px-4 sm:px-6 py-4 sm:py-6 h-auto text-sm sm:text-base rounded-lg shadow-lg shadow-purple-900/20 flex items-center gap-2">
                  Book Free Discovery Call
                  <span className="text-lg">→</span>
                </Button>
              </Link>
              
              <Link href="/projects">
                <Button variant="outline" className="border-purple-800/30 text-white hover:bg-purple-900/10 px-4 sm:px-6 py-4 sm:py-6 h-auto text-sm sm:text-base rounded-lg">
                  View Case Studies
                </Button>
              </Link>
            </motion.div>
          </div>

          {/* Right content - Business metrics - more compact */}
          <motion.div 
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.4 }}
            className="lg:col-span-5 mt-6 sm:mt-0"
          >
            <div className="bg-black/60 backdrop-blur-lg border border-purple-900/20 rounded-xl p-4 sm:p-6 shadow-xl">
              <h3 className="text-base sm:text-lg text-white font-semibold mb-3 sm:mb-5 flex items-center gap-2">
                <span className="text-purple-400">★</span> Our Impact in Numbers
              </h3>
              
              <div className="grid grid-cols-2 gap-3 sm:gap-4">
                <div className="p-2 sm:p-3 rounded-lg bg-black/30 border border-purple-900/20">
                  <CounterAnimation end={55} suffix="%" label="Higher Conversion" />
                </div>
                <div className="p-2 sm:p-3 rounded-lg bg-black/30 border border-purple-900/20">
                  <CounterAnimation end={200} suffix="+" label="Satisfied Clients" />
                </div>
                <div className="p-2 sm:p-3 rounded-lg bg-black/30 border border-purple-900/20">
                  <CounterAnimation end={40} suffix="%" label="Cost Reduction" />
                </div>
                <div className="p-2 sm:p-3 rounded-lg bg-black/30 border border-purple-900/20">
                  <CounterAnimation end={83} suffix="%" label="Client Retention" />
                </div>
              </div>

              {/* Tech skills showcase - simplified */}
              <div className="mt-3 sm:mt-5 pt-3 sm:pt-5 border-t border-purple-900/20">
                <h4 className="text-sm sm:text-base text-white/80 font-medium mb-2 sm:mb-3">Core Technologies</h4>
                <div className="space-y-1.5 sm:space-y-2">
                  {coreTechnologies.map((tech, i) => (
                    <div key={i} className="space-y-1">
                      <div className="flex justify-between text-xs">
                        <span className="text-gray-400">{tech.name}</span>
                        <span className="text-gray-400">{tech.level}%</span>
                      </div>
                      <div className="h-1 bg-black/50 rounded-full overflow-hidden">
                        <motion.div
                          initial={{ width: 0 }}
                          animate={{ width: `${tech.level}%` }}
                          transition={{ duration: 1, delay: 0.5 + (i * 0.1) }}
                          className={`h-full ${tech.color} rounded-full`}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Advanced features showcase - simplified */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6 }}
          className="mt-12 sm:mt-20"
        >
          <div className="text-center mb-6 sm:mb-10">
            <h2 className="text-xl sm:text-2xl font-bold text-white mb-2 sm:mb-3">Advanced Technology Solutions</h2>
            <p className="text-sm sm:text-base text-gray-400 max-w-2xl mx-auto">Cutting-edge technologies to give your business a competitive advantage</p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
            {advancedFeatures.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7 + index * 0.1 }}
                className="bg-black/40 backdrop-blur-lg border border-purple-900/20 rounded-lg p-3 sm:p-4"
              >
                <div className="flex items-start gap-2 sm:gap-3 mb-2 sm:mb-3">
                  <div className="flex-shrink-0 w-7 h-7 sm:w-9 sm:h-9 rounded-lg bg-purple-900/20 flex items-center justify-center text-base sm:text-lg">
                    {feature.icon}
                  </div>
                  <div>
                    <h3 className="text-sm sm:text-base font-semibold text-white mb-0.5 sm:mb-1">{feature.title}</h3>
                    <p className="text-xs sm:text-sm text-gray-400">{feature.description}</p>
                  </div>
                </div>
                
                <div className="pl-9 sm:pl-12">
                  <div className="flex flex-wrap gap-1.5 sm:gap-2">
                    {feature.details.map((detail, i) => (
                      <span 
                        key={i}
                        className="px-1.5 sm:px-2 py-0.5 sm:py-1 text-[10px] sm:text-xs rounded-full bg-black/30 border border-purple-900/20 text-gray-300"
                      >
                        {detail}
                      </span>
                    ))}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
        
        {/* Business benefits - simplified */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="mt-10 sm:mt-16"
        >
          <div className="text-center mb-6 sm:mb-10">
            <h2 className="text-xl sm:text-2xl font-bold text-white">How We Help Your Business Grow</h2>
            <p className="text-sm sm:text-base text-gray-400 mt-1 sm:mt-2 max-w-2xl mx-auto">Our comprehensive approach helps your business thrive</p>
          </div>
          
          <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-4 gap-3 sm:gap-4">
            {businessBenefits.map((benefit, index) => (
              <motion.div
                key={benefit.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7 + index * 0.1 }}
                className="bg-black/40 backdrop-blur-lg border border-purple-900/20 rounded-lg p-3 sm:p-4 relative overflow-hidden"
              >
                <div className="relative">
                  <div className="w-7 h-7 sm:w-9 sm:h-9 flex items-center justify-center rounded-lg bg-purple-900/20 border border-purple-900/30 mb-2 sm:mb-3 text-base sm:text-lg">
                    {benefit.icon}
                  </div>
                  <h3 className="text-sm sm:text-base font-semibold text-white mb-1 sm:mb-2">{benefit.title}</h3>
                  <p className="text-xs sm:text-sm text-gray-400">{benefit.description}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
        
        {/* NEX-SHFT Methodology Flowchart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="mt-12 sm:mt-20 pt-5 sm:pt-8 border-t border-purple-900/20 relative overflow-hidden"
        >
          {/* Fast Moving Lines Background */}
          <div className="absolute inset-0 z-0 pointer-events-none">
            <FastMovingLines
              lineCount={20}
              speed={3}
              color="#8b5cf6"
              className="opacity-40"
            />
          </div>

          {/* Content */}
          <div className="relative z-10">
            <NexShiftFlowchart />
          </div>
        </motion.div>

        {/* CTA section - simplified */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="mt-10 sm:mt-16 relative rounded-xl p-5 sm:p-8 text-center overflow-hidden"
        >
          <div className="absolute inset-0 bg-black/40 border border-purple-900/20 rounded-xl z-0"></div>
          
          <div className="relative z-10">
            <h2 className="text-xl sm:text-2xl font-bold text-white mb-3 sm:mb-4">Ready to Transform Your Business?</h2>
            <p className="text-sm sm:text-base text-gray-300 max-w-2xl mx-auto mb-4 sm:mb-6">Book a free discovery call to explore how we can help achieve your business goals.</p>
            
            <div className="flex flex-wrap gap-3 sm:gap-4 justify-center">
              <Link href="/discovery-call">
                <Button className="bg-purple-700 hover:bg-purple-800 text-white px-4 sm:px-5 py-4 sm:py-5 h-auto text-sm sm:text-base rounded-lg shadow-lg">
                  Schedule Your Free Call
                  <span className="ml-2">→</span>
                </Button>
              </Link>
              
              <Link href="/services">
                <Button variant="outline" className="border-purple-900/30 text-white hover:bg-purple-900/10 px-4 sm:px-5 py-4 sm:py-5 h-auto text-sm sm:text-base rounded-lg">
                  Explore All Services
                </Button>
              </Link>
            </div>
          </div>
        </motion.div>
      </div>
    </motion.section>
  )
} 