/* GSAP-based animation utility classes */
/* These replace the @keyframes animations for better performance */

/* Base performance optimizations */
.gsap-optimized {
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  will-change: transform;
}

/* Float animations - will be controlled by GSAP */
.gsap-float {
  /* Initial state - GSAP will handle the animation */
  transform: translateY(0) translateZ(0);
}

.gsap-float-delayed {
  /* Initial state - GSAP will handle the animation */
  transform: translateY(0) translateZ(0);
}

.gsap-float-slow {
  /* Initial state - GSAP will handle the animation */
  transform: translate(0, 0) translateZ(0);
}

.gsap-float-slower {
  /* Initial state - GSAP will handle the animation */
  transform: translate(0, 0) translateZ(0);
}

/* Fade animations - will be controlled by GSAP */
.gsap-fade-in {
  opacity: 0;
}

.gsap-fade-in-up {
  opacity: 0;
  transform: translateY(10px);
}

/* Scale animations - will be controlled by GSAP */
.gsap-scale-in {
  opacity: 0;
  transform: scale(0.9);
}

/* Slide animations - will be controlled by GSAP */
.gsap-slide-up {
  opacity: 0;
  transform: translateY(50px);
}

.gsap-slide-down {
  opacity: 0;
  transform: translateY(-50px);
}

.gsap-slide-left {
  opacity: 0;
  transform: translateX(50px);
}

.gsap-slide-right {
  opacity: 0;
  transform: translateX(-50px);
}

/* Glow animations - will be controlled by GSAP */
.gsap-glow-pulse {
  box-shadow: 0 0 10px 1px rgba(147, 51, 234, 0.4);
}

/* Shimmer effect - will be controlled by GSAP */
.gsap-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  background-size: 200% 100%;
  background-position: -200% 0;
}

/* Morph animations - will be controlled by GSAP */
.gsap-morph-bounce {
  transform: scale(1);
}

/* Ripple effect - will be controlled by GSAP */
.gsap-ripple {
  position: relative;
  overflow: hidden;
}

.gsap-ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  pointer-events: none;
}

/* Blob animation - will be controlled by GSAP */
.gsap-blob {
  transform: translate(0px, 0px) scale(1);
}

/* Gradient animation - will be controlled by GSAP */
.gsap-gradient-animation {
  background-position: 0% 50%;
}

/* Mesh float animation - will be controlled by GSAP */
.gsap-mesh-float {
  background-position: 0 0;
}

/* Accordion animations - will be controlled by GSAP */
.gsap-accordion-down {
  height: 0;
  overflow: hidden;
}

.gsap-accordion-up {
  overflow: hidden;
}

/* Hover effects that work with GSAP */
.gsap-hover-lift {
  transition: none; /* Disable CSS transitions, let GSAP handle it */
}

/* Performance utilities */
.gsap-will-change-transform {
  will-change: transform;
}

.gsap-will-change-opacity {
  will-change: opacity;
}

.gsap-will-change-auto {
  will-change: auto;
}

.gsap-backface-hidden {
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.gsap-transform-gpu {
  transform: translate3d(0, 0, 0);
}

/* Stagger animation containers */
.gsap-stagger-container > * {
  opacity: 0;
  transform: translateY(30px);
}

/* Modal animation states */
.gsap-modal-enter {
  opacity: 0;
  transform: scale(0.9) translateY(20px);
}

.gsap-modal-exit {
  opacity: 1;
  transform: scale(1) translateY(0);
}

/* Page transition states */
.gsap-page-enter {
  opacity: 0;
  transform: translateY(100px);
}

.gsap-page-exit {
  opacity: 1;
  transform: translateY(0);
}

/* Loading states */
.gsap-loading-pulse {
  opacity: 0.5;
  transform: scale(1);
}

.gsap-loading-spin {
  transform: rotate(0deg);
}

/* Text animations */
.gsap-text-reveal {
  opacity: 0;
  transform: translateY(20px);
}

.gsap-text-typewriter {
  overflow: hidden;
  white-space: nowrap;
  width: 0;
}

/* Button animations */
.gsap-button-press {
  transform: scale(1);
}

.gsap-button-hover {
  transform: translateY(0);
}

/* Card animations */
.gsap-card-flip {
  transform: rotateY(0deg);
  backface-visibility: hidden;
}

.gsap-card-slide {
  transform: translateX(0);
}

/* Navigation animations */
.gsap-nav-slide {
  transform: translateX(-100%);
}

.gsap-nav-fade {
  opacity: 0;
}

/* Progress animations */
.gsap-progress-bar {
  width: 0%;
}

.gsap-progress-circle {
  stroke-dashoffset: 283; /* Circumference for a circle with radius 45 */
}

/* Particle animations */
.gsap-particle {
  opacity: 0;
  transform: scale(0) translate(0, 0);
}

/* Elastic animations */
.gsap-elastic {
  transform: scale(1);
}

/* Bounce animations */
.gsap-bounce {
  transform: translateY(0);
}

/* Swing animations */
.gsap-swing {
  transform: rotate(0deg);
  transform-origin: top center;
}

/* Flip animations */
.gsap-flip-x {
  transform: rotateX(0deg);
}

.gsap-flip-y {
  transform: rotateY(0deg);
}

/* Zoom animations */
.gsap-zoom-in {
  opacity: 0;
  transform: scale(0.3);
}

.gsap-zoom-out {
  opacity: 1;
  transform: scale(1);
}

/* Slide reveal animations */
.gsap-slide-reveal {
  overflow: hidden;
}

.gsap-slide-reveal::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: currentColor;
  transform: translateX(-100%);
}

/* Counter animations */
.gsap-counter {
  /* Will be animated via GSAP's text plugin or custom counter */
}

/* Path drawing animations */
.gsap-draw-path {
  stroke-dasharray: 1000;
  stroke-dashoffset: 1000;
}

/* Morphing shapes */
.gsap-morph-shape {
  /* SVG path morphing will be handled by GSAP */
}

/* 3D animations */
.gsap-3d-rotate {
  transform: perspective(1000px) rotateX(0deg) rotateY(0deg);
}

.gsap-3d-flip {
  transform: perspective(1000px) rotateY(0deg);
}

/* Magnetic effect */
.gsap-magnetic {
  transform: translate(0, 0);
}

/* Parallax elements */
.gsap-parallax {
  transform: translateY(0);
}

/* Scroll-triggered animations */
.gsap-scroll-trigger {
  opacity: 0;
  transform: translateY(50px);
}

/* Performance hint for reduced motion */
@media (prefers-reduced-motion: reduce) {
  .gsap-respect-motion {
    animation: none !important;
    transition: none !important;
  }
  
  .gsap-respect-motion * {
    animation: none !important;
    transition: none !important;
  }
}
