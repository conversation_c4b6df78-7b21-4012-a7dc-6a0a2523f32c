import { Inter } from "next/font/google"
import "./globals.css"
import { cn } from "@/lib/utils"
import Footer from "@/components/layout/Footer"
import { ThemeProvider } from "@/components/ThemeProvider"
import Navbar from "@/components/layout/Navbar"
import Easter<PERSON>gg<PERSON>ounter from "@/components/layout/EasterEggCounter"
import { EasterEggProvider } from "@/context/EasterEggContext"
import type { Metadata } from 'next'
import { CurrencyProvider } from '@/app/contexts/CurrencyContext'
import { TimelineProvider } from '@/app/contexts/TimelineContext'
import MobilePopup from './components/MobilePopup'
import { Analytics } from "@vercel/analytics/react"
import { initializeDatabase } from './lib/database-init'
import NexiousChatbotWrapper from '@/components/NexiousChatbotWrapper'
import GSAPInitializer from '@/components/GSAPInitializer'
import GSAPPerformanceMonitor from '@/components/GSAPPerformanceMonitor'

// Initialize the database when the server starts
if (typeof window === 'undefined') {
  initializeDatabase()
    .then(() => console.log('Database initialized in layout'))
    .catch(err => console.error('Failed to initialize database:', err));
}

// Optimize font loading - add display: 'swap' to show text with fallback font while custom font loads
const inter = Inter({ 
  subsets: ["latin"],
  display: 'swap',
  preload: true,
  fallback: ['system-ui', 'sans-serif'],
  variable: '--font-inter',
})

export const metadata: Metadata = {
  title: 'NEX-DEVS | Professional Web Development Solutions',
  description: 'NEX-DEVS provides professional web development services including custom websites, applications, and digital solutions for businesses of all sizes.',
  metadataBase: new URL('https://your-domain.com'), // Replace with your actual domain
  openGraph: {
    title: "NEX-WEBS - Professional Web Solutions",
    description: "Professional website developer specializing in WordPress, Shopify, and custom solutions.",
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "NEX-DEVS - Professional Web Solutions",
    description: "Professional website developer specializing in WordPress, Shopify, and custom solutions.",
  },
  icons: {
    icon: [
      { url: '/icons/favicon.svg', type: 'image/svg+xml' },
      { url: '/icons/favicon-192.png', type: 'image/png', sizes: '192x192' },
      { url: '/icons/favicon-512.png', type: 'image/png', sizes: '512x512' },
    ],
    shortcut: '/icons/favicon.svg',
    apple: '/icons/favicon.svg',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning className={inter.variable}>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/icons/favicon.svg" type="image/svg+xml" />
        <link rel="shortcut icon" href="/icons/favicon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/icons/favicon.svg" />
        <link rel="manifest" href="/manifest.json" />
        {/* Preload critical fonts */}
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />
      </head>
      <body className={cn(inter.className, "min-h-screen bg-background text-foreground flex flex-col smooth-scroll")}>
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
          disableTransitionOnChange
        >
          <CurrencyProvider>
            <TimelineProvider>
              <EasterEggProvider>
                <MobilePopup />
                <Navbar />
                <div id="easter-egg-counter-container">
                  <EasterEggCounter hiddenOnPaths={['/pricing']} />
                </div>
                <div className="flex-1">
                  {children}
                </div>
                <Footer />
                <NexiousChatbotWrapper />
                <GSAPInitializer />
                <GSAPPerformanceMonitor />
                <Analytics />
              </EasterEggProvider>
            </TimelineProvider>
          </CurrencyProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
