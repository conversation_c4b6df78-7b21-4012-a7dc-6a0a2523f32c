'use client';

import { gsap } from 'gsap';

// Initialize GSAP animations to replace CSS keyframes
export class GSAPAnimations {
  private static initialized = false;

  // Initialize all GSAP animations
  static init() {
    if (this.initialized || typeof window === 'undefined') return;
    
    // Set up GSAP defaults for performance
    gsap.config({
      force3D: true,
      nullTargetWarn: false,
    });

    // Initialize all animation types
    this.initFloatAnimations();
    this.initFadeAnimations();
    this.initScaleAnimations();
    this.initSlideAnimations();
    this.initGlowAnimations();
    this.initShimmerAnimations();
    this.initBlobAnimations();
    this.initGradientAnimations();
    this.initMeshFloatAnimations();
    this.initHoverEffects();
    this.initLoadingAnimations();

    this.initialized = true;
  }

  // Float animations (replaces CSS keyframes)
  static initFloatAnimations() {
    // Float animation
    gsap.utils.toArray('.gsap-float').forEach((element: any) => {
      gsap.to(element, {
        y: -10,
        duration: 2,
        ease: "power1.inOut",
        yoyo: true,
        repeat: -1,
        force3D: true
      });
    });

    // Float delayed animation
    gsap.utils.toArray('.gsap-float-delayed').forEach((element: any, index: number) => {
      gsap.to(element, {
        y: -8,
        duration: 2.5,
        ease: "power1.inOut",
        yoyo: true,
        repeat: -1,
        delay: index * 0.2,
        force3D: true
      });
    });

    // Float slow animation
    gsap.utils.toArray('.gsap-float-slow').forEach((element: any) => {
      gsap.to(element, {
        x: 5,
        y: -5,
        duration: 3,
        ease: "power1.inOut",
        yoyo: true,
        repeat: -1,
        force3D: true
      });
    });

    // Float slower animation
    gsap.utils.toArray('.gsap-float-slower').forEach((element: any) => {
      gsap.to(element, {
        x: -5,
        y: -5,
        duration: 4,
        ease: "power1.inOut",
        yoyo: true,
        repeat: -1,
        force3D: true
      });
    });
  }

  // Fade animations
  static initFadeAnimations() {
    gsap.utils.toArray('.gsap-fade-in').forEach((element: any) => {
      gsap.fromTo(element, 
        { opacity: 0 },
        { 
          opacity: 1, 
          duration: 0.5,
          ease: "power2.out",
          force3D: true
        }
      );
    });

    gsap.utils.toArray('.gsap-fade-in-up').forEach((element: any) => {
      gsap.fromTo(element,
        { opacity: 0, y: 10 },
        { 
          opacity: 1, 
          y: 0,
          duration: 0.5,
          ease: "power2.out",
          force3D: true
        }
      );
    });
  }

  // Scale animations
  static initScaleAnimations() {
    gsap.utils.toArray('.gsap-scale-in').forEach((element: any) => {
      gsap.fromTo(element,
        { opacity: 0, scale: 0.9 },
        { 
          opacity: 1, 
          scale: 1,
          duration: 0.5,
          ease: "back.out(1.7)",
          force3D: true
        }
      );
    });
  }

  // Slide animations
  static initSlideAnimations() {
    gsap.utils.toArray('.gsap-slide-up').forEach((element: any) => {
      gsap.fromTo(element,
        { opacity: 0, y: 50 },
        { 
          opacity: 1, 
          y: 0,
          duration: 0.6,
          ease: "power2.out",
          force3D: true
        }
      );
    });

    gsap.utils.toArray('.gsap-slide-down').forEach((element: any) => {
      gsap.fromTo(element,
        { opacity: 0, y: -50 },
        { 
          opacity: 1, 
          y: 0,
          duration: 0.6,
          ease: "power2.out",
          force3D: true
        }
      );
    });

    gsap.utils.toArray('.gsap-slide-left').forEach((element: any) => {
      gsap.fromTo(element,
        { opacity: 0, x: 50 },
        { 
          opacity: 1, 
          x: 0,
          duration: 0.6,
          ease: "power2.out",
          force3D: true
        }
      );
    });

    gsap.utils.toArray('.gsap-slide-right').forEach((element: any) => {
      gsap.fromTo(element,
        { opacity: 0, x: -50 },
        { 
          opacity: 1, 
          x: 0,
          duration: 0.6,
          ease: "power2.out",
          force3D: true
        }
      );
    });
  }

  // Glow animations
  static initGlowAnimations() {
    gsap.utils.toArray('.gsap-glow-pulse').forEach((element: any) => {
      gsap.to(element, {
        boxShadow: "0 0 20px 5px rgba(147, 51, 234, 0.7)",
        duration: 2,
        ease: "power1.inOut",
        yoyo: true,
        repeat: -1
      });
    });
  }

  // Shimmer animations
  static initShimmerAnimations() {
    gsap.utils.toArray('.gsap-shimmer').forEach((element: any) => {
      gsap.to(element, {
        backgroundPosition: "200% 0",
        duration: 8,
        ease: "none",
        repeat: -1
      });
    });
  }

  // Blob animations (replaces CSS blob keyframes)
  static initBlobAnimations() {
    gsap.utils.toArray('.gsap-blob').forEach((element: any) => {
      const tl = gsap.timeline({ repeat: -1 });
      tl.to(element, {
        x: 30,
        y: -30,
        scale: 1.1,
        duration: 2,
        ease: "power1.inOut"
      })
      .to(element, {
        x: -20,
        y: 20,
        scale: 0.9,
        duration: 2,
        ease: "power1.inOut"
      })
      .to(element, {
        x: 0,
        y: 0,
        scale: 1,
        duration: 2,
        ease: "power1.inOut"
      });
    });
  }

  // Gradient animations
  static initGradientAnimations() {
    gsap.utils.toArray('.gsap-gradient-animation').forEach((element: any) => {
      gsap.to(element, {
        backgroundPosition: "100% 50%",
        duration: 3,
        ease: "power1.inOut",
        yoyo: true,
        repeat: -1
      });
    });
  }

  // Mesh float animations
  static initMeshFloatAnimations() {
    gsap.utils.toArray('.gsap-mesh-float').forEach((element: any) => {
      gsap.to(element, {
        backgroundPosition: "200px 200px",
        duration: 20,
        ease: "none",
        repeat: -1
      });
    });
  }

  // Hover effects
  static initHoverEffects() {
    gsap.utils.toArray('.gsap-hover-lift').forEach((element: any) => {
      const hoverTween = gsap.to(element, {
        y: -5,
        duration: 0.3,
        ease: "power2.out",
        paused: true,
        force3D: true
      });

      element.addEventListener('mouseenter', () => hoverTween.play());
      element.addEventListener('mouseleave', () => hoverTween.reverse());
    });
  }

  // Loading animations
  static initLoadingAnimations() {
    gsap.utils.toArray('.gsap-loading-pulse').forEach((element: any) => {
      gsap.to(element, {
        opacity: 1,
        scale: 1.05,
        duration: 1,
        ease: "power1.inOut",
        yoyo: true,
        repeat: -1
      });
    });

    gsap.utils.toArray('.gsap-loading-spin').forEach((element: any) => {
      gsap.to(element, {
        rotation: 360,
        duration: 1,
        ease: "none",
        repeat: -1
      });
    });
  }

  // Stagger animations
  static staggerIn(selector: string, options: any = {}) {
    return gsap.fromTo(selector,
      { opacity: 0, y: 30 },
      { 
        opacity: 1, 
        y: 0, 
        duration: 0.6,
        ease: "power2.out",
        stagger: 0.1,
        force3D: true,
        ...options 
      }
    );
  }

  // Modal animations
  static modalEnter(element: HTMLElement | string, options: any = {}) {
    return gsap.fromTo(element,
      { opacity: 0, scale: 0.9, y: 20 },
      { 
        opacity: 1, 
        scale: 1, 
        y: 0,
        duration: 0.3,
        ease: "power2.out",
        force3D: true,
        ...options 
      }
    );
  }

  static modalExit(element: HTMLElement | string, options: any = {}) {
    return gsap.to(element, { 
      opacity: 0, 
      scale: 0.9, 
      y: -20,
      duration: 0.2,
      ease: "power2.in",
      force3D: true,
      ...options 
    });
  }

  // Page transitions
  static pageEnter(element: HTMLElement | string, options: any = {}) {
    return gsap.fromTo(element,
      { opacity: 0, y: 100 },
      { 
        opacity: 1, 
        y: 0, 
        duration: 0.8,
        ease: "power3.out",
        force3D: true,
        ...options 
      }
    );
  }

  static pageExit(element: HTMLElement | string, options: any = {}) {
    return gsap.to(element, { 
      opacity: 0, 
      y: -100, 
      duration: 0.5,
      ease: "power3.in",
      force3D: true,
      ...options 
    });
  }

  // Cleanup function
  static cleanup() {
    gsap.killTweensOf("*");
    this.initialized = false;
  }

  // Refresh animations (useful for dynamic content)
  static refresh() {
    this.cleanup();
    this.init();
  }
}

// Auto-initialize when DOM is ready
if (typeof window !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => GSAPAnimations.init());
  } else {
    GSAPAnimations.init();
  }
}

export default GSAPAnimations;
