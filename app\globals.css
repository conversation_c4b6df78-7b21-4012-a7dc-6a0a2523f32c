@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import GSAP-based animations */
@import '../styles/gsap-animations.css';

@layer base {
  :root {
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
 
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
 
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
 
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
 
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
 
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
 
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
 
    --radius: 0.5rem;
  }
 
  .dark {
    --background: 0 0% 0%;
    --foreground: 210 40% 98%;
 
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
 
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
 
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
 
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
 
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
 
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
 
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
 
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}
 
@layer base {
  * {
    @apply border-border;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  html {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    height: 100%;
    overflow-x: hidden;
    scroll-padding-top: 80px;
    position: relative;
    width: 100%;
  }
  
  body {
    @apply bg-background text-foreground;
    background-color: black;
    background-image: linear-gradient(to bottom right, rgba(76, 29, 149, 0.1), black 30%, black 70%, rgba(91, 33, 182, 0.1));
    color: white;
    min-height: 100vh;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: fixed;
    inset: 0;
    overflow-y: scroll;
    overflow-x: hidden;
    width: 100%;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: none;
  }

  #__next {
    isolation: isolate;
    position: relative;
    width: 100%;
    min-height: 100%;
    overflow-x: clip;
  }

  main {
    position: relative;
    width: 100%;
    flex: 1;
    z-index: 1;
  }

  /* Lock body when modal is open */
  body.modal-open {
    overflow: hidden;
    padding-right: var(--scrollbar-width);
  }

  /* Simple Scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(147, 51, 234, 0.2);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(147, 51, 234, 0.4);
  }

  /* Firefox scrollbar */
  * {
    scrollbar-width: thin;
    scrollbar-color: rgba(147, 51, 234, 0.2) transparent;
  }
}

/* Simplified animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Float animations now handled by GSAP - see lib/gsap-animations.ts */
/* Use .gsap-float, .gsap-float-delayed, .gsap-float-slow, .gsap-float-slower classes */

/* Fade animations now handled by GSAP - see lib/gsap-animations.ts */
/* Use .gsap-fade-in, .gsap-fade-in-up classes */

/* Simplified utility classes */
.transform-gpu {
  transform: translateY(0);
}

.scroll-smooth {
  scroll-behavior: smooth;
}

/* Custom utility classes for animations */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate3d(0, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* Image optimization classes */
.image-wrapper {
  @apply relative overflow-hidden;
}

.image-wrapper img {
  @apply transition-transform duration-300;
}

/* Accessibility improvements */
.sr-only {
  @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
}

.focus-visible {
  @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
}

/* Line clamp utilities */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Easter Egg Animations */
@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes glitch {
  0% {
    transform: translate(0);
  }
  20% {
    transform: translate(-2px, 2px);
  }
  40% {
    transform: translate(-2px, -2px);
  }
  60% {
    transform: translate(2px, 2px);
  }
  80% {
    transform: translate(2px, -2px);
  }
  100% {
    transform: translate(0);
  }
}

.animate-bounce {
  animation: bounce 0.5s ease-in-out infinite;
}

.animate-spin {
  animation: spin 2s linear infinite;
}

.animate-glitch {
  animation: glitch 0.3s ease-in-out infinite;
}

/* Terminal styling */
.terminal-text {
  color: #00ff00;
  text-shadow: 0 0 5px #00ff00;
  font-family: 'Courier New', monospace;
}

.terminal-cursor {
  animation: blink 1s step-end infinite;
}

@keyframes blink {
  from, to {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

/* Easter Egg Animation */
.dW5sb2NrLXRoZS1tYXRyaXg {
  animation: matrix-rain 2s ease-in forwards;
  position: relative;
  overflow: hidden;
}

.dW5sb2NrLXRoZS1tYXRyaXg::before {
  content: '';
  position: absolute;
  top: -100%;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, 
    rgba(0, 255, 0, 0.2) 0%,
    rgba(0, 255, 0, 0.1) 50%,
    transparent 100%
  );
  animation: matrix-rain-effect 1.5s ease-out forwards;
}

@keyframes matrix-rain-effect {
  0% {
    top: -100%;
  }
  100% {
    top: 100%;
  }
}

@keyframes matrix-rain {
  0% {
    text-shadow: 0 0 0px #0f0;
  }
  50% {
    text-shadow: 0 0 10px #0f0;
  }
  100% {
    text-shadow: 0 0 5px #0f0;
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes float-delayed {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-15px); }
}

@keyframes float-slow {
  0%, 100% { transform: translate(0, 0); }
  50% { transform: translate(10px, -10px); }
}

@keyframes float-slower {
  0%, 100% { transform: translate(0, 0); }
  50% { transform: translate(-15px, -15px); }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float-delayed 4s ease-in-out infinite;
}

.animate-float-slow {
  animation: float-slow 6s ease-in-out infinite;
}

.animate-float-slower {
  animation: float-slower 8s ease-in-out infinite;
}

.animate-fade-in-up {
  animation: fade-in-up 1s ease-out forwards;
}

/* Animation Delays */
.animation-delay-100 {
  animation-delay: 100ms;
}

.animation-delay-200 {
  animation-delay: 200ms;
}

.animation-delay-300 {
  animation-delay: 300ms;
}

.animation-delay-400 {
  animation-delay: 400ms;
}

.animation-delay-500 {
  animation-delay: 500ms;
}

.animation-delay-700 {
  animation-delay: 700ms;
}

/* Animation Durations for Timing Control */
.animation-duration-500 {
  animation-duration: 500ms !important;
}

.animation-duration-1000 {
  animation-duration: 1000ms !important;
}

.animation-duration-1500 {
  animation-duration: 1500ms !important;
}

.animation-duration-2000 {
  animation-duration: 2000ms !important;
}

.animation-duration-3000 {
  animation-duration: 3000ms !important;
}

.animation-duration-5000 {
  animation-duration: 5000ms !important;
}

.animation-duration-8000 {
  animation-duration: 8000ms !important;
}

.animation-duration-10000 {
  animation-duration: 10000ms !important;
}

/* Animation Timing Function Controls */
.animation-ease {
  animation-timing-function: ease !important;
}

.animation-ease-in {
  animation-timing-function: ease-in !important;
}

.animation-ease-out {
  animation-timing-function: ease-out !important;
}

.animation-ease-in-out {
  animation-timing-function: ease-in-out !important;
}

.animation-linear {
  animation-timing-function: linear !important;
}

.animation-bezier {
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.animation-spring {
  animation-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55) !important;
}

/* Optimize animations with transform-gpu */
.transform-gpu {
  transform: translate3d(0, 0, 0);
}

/* Refined animations now handled by GSAP - see lib/gsap-animations.ts */
/* Use .gsap-float, .gsap-float-delayed, .gsap-float-slow, .gsap-float-slower classes */

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(10px) translateZ(0);
  }
  to {
    opacity: 1;
    transform: translateY(0) translateZ(0);
  }
}

/* Typing Animation */
@keyframes typing {
  from { width: 0 }
  to { width: 100% }
}

@keyframes blink-caret {
  from, to { border-color: transparent }
  50% { border-color: rgb(168, 85, 247, 0.5) }
}

@keyframes reveal-text {
  0% {
    clip-path: inset(0 100% 0 0);
  }
  100% {
    clip-path: inset(0 0 0 0);
  }
}

.animate-typing {
  overflow: hidden;
  white-space: nowrap;
  border-right: 2px solid;
  animation: 
    typing 3.5s steps(40, end),
    blink-caret .75s step-end infinite;
}

.animate-reveal {
  animation: reveal-text 1.5s cubic-bezier(0.77, 0, 0.175, 1) forwards;
}

.animate-reveal-delay-1 {
  animation: reveal-text 1.5s cubic-bezier(0.77, 0, 0.175, 1) 0.2s forwards;
  clip-path: inset(0 100% 0 0);
}

.animate-reveal-delay-2 {
  animation: reveal-text 1.5s cubic-bezier(0.77, 0, 0.175, 1) 0.4s forwards;
  clip-path: inset(0 100% 0 0);
}

/* Improved floating animations */
@keyframes float-smooth {
  0%, 100% {
    transform: translateY(0) translateX(0) rotate(0);
  }
  25% {
    transform: translateY(-5px) translateX(3px) rotate(2deg);
  }
  75% {
    transform: translateY(5px) translateX(-3px) rotate(-2deg);
  }
}

.animate-float-smooth {
  animation: float-smooth 6s ease-in-out infinite;
}

/* Glow Effects */
.glow-text {
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.1),
               0 0 40px rgba(255, 255, 255, 0.1);
}

.glow-text-sm {
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.1),
               0 0 20px rgba(255, 255, 255, 0.1);
}

.shadow-glow {
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.05),
              0 0 40px rgba(255, 255, 255, 0.05);
}

.shadow-glow-sm {
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
}

.hover\:shadow-glow:hover {
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.1),
              0 0 40px rgba(255, 255, 255, 0.1);
}

.hover\:shadow-glow-sm:hover {
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.15);
}

/* Purple Glow Effects */
.glow-text-purple {
  text-shadow: 0 0 20px rgba(147, 51, 234, 0.3),
               0 0 40px rgba(147, 51, 234, 0.2),
               0 0 60px rgba(147, 51, 234, 0.1);
}

.glow-text-purple-sm {
  text-shadow: 0 0 10px rgba(147, 51, 234, 0.2),
               0 0 20px rgba(147, 51, 234, 0.1);
}

.shadow-purple-glow {
  box-shadow: 0 0 20px rgba(147, 51, 234, 0.15),
              0 0 40px rgba(147, 51, 234, 0.1),
              0 0 60px rgba(147, 51, 234, 0.05);
}

.shadow-purple-glow-sm {
  box-shadow: 0 0 15px rgba(147, 51, 234, 0.2),
              0 0 30px rgba(147, 51, 234, 0.1);
}

.hover\:shadow-purple-glow:hover {
  box-shadow: 0 0 25px rgba(147, 51, 234, 0.2),
              0 0 50px rgba(147, 51, 234, 0.15),
              0 0 75px rgba(147, 51, 234, 0.1);
}

.hover\:shadow-purple-glow-sm:hover {
  box-shadow: 0 0 20px rgba(147, 51, 234, 0.25),
              0 0 40px rgba(147, 51, 234, 0.15);
}

/* GSAP Performance optimized utilities */
@layer utilities {
  .gsap-transform-gpu {
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }

  .gsap-will-change-transform {
    will-change: transform;
  }

  .gsap-will-change-opacity {
    will-change: opacity;
  }

  .gsap-will-change-auto {
    will-change: auto;
  }

  .gsap-optimized {
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    will-change: transform;
  }

  /* 60fps optimized classes */
  .gsap-60fps {
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    will-change: transform, opacity;
  }

  .bg-grid-white\/5 {
    background-image: linear-gradient(to right, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                     linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  }
  
  .bg-grid-16 {
    background-size: 32px 32px;
  }

  .animate-accordion-down {
    animation: accordion-down 0.2s ease-out;
  }

  .animate-accordion-up {
    animation: accordion-up 0.2s ease-out;
  }
}

/* Professional GSAP Performance Optimizations */
html {
  scroll-behavior: auto; /* Let GSAP handle smooth scrolling */
}

body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Hardware acceleration for animated elements only */
.gsap-animate,
.gsap-optimized,
.gsap-60fps,
.gsap-professional-mode * {
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* GSAP Animation Classes */
.gsap-fade-in-scroll,
.gsap-slide-in-scroll,
.gsap-stagger-scroll,
.gsap-scale-scroll,
.gsap-parallax {
  opacity: 0;
  will-change: transform, opacity;
}

/* Disable expensive CSS animations - let GSAP handle them */
.animate-fade-in,
.animate-slide-up,
.animate-bounce,
.animate-spin,
.animate-glitch,
.animate-float,
.animate-float-delayed,
.animate-float-slow,
.animate-float-slower,
.animate-fade-in-up,
.animate-typing,
.animate-reveal,
.animate-reveal-delay-1,
.animate-reveal-delay-2,
.animate-float-smooth,
.animate-accordion-down,
.animate-accordion-up,
.animate-ripple,
.animate-shimmer,
.animate-morph-bounce,
.animate-glow {
  animation: none !important;
}

/* Mobile Animation Control - GSAP Optimized */
@media (max-width: 768px) {
  /* Disable heavy animations on mobile for better performance */
  * {
    will-change: auto !important;
    animation-duration: 0.2s !important;
    transition-duration: 0.15s !important;
  }

  /* Let GSAP handle mobile animations */
  .gsap-mobile-optimized {
    will-change: auto;
  }

  /* Disable heavy CSS animations on mobile */
  .disable-css-animation-mobile {
    animation: none !important;
    transition: none !important;
  }

  /* GSAP will handle essential animations */
  .gsap-preserve-mobile {
    transform: translateZ(0);
  }
}

/* Add smooth transitions */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* Enhanced backdrop blur - OPTIMIZED FOR PERFORMANCE */
.backdrop-blur-md {
  /* backdrop-filter: blur(12px); */
  /* -webkit-backdrop-filter: blur(12px); */
  background: rgba(0, 0, 0, 0.1);
}

/* Gradient text enhancement */
.bg-clip-text {
  -webkit-background-clip: text;
  background-clip: text;
}

/* Accordion animations */
@keyframes accordion-down {
  from {
    height: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }
  to {
    height: 0;
  }
}

/* Modern Visual Effects - Optimized for Performance */
.morph-transition {
  position: relative;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
  will-change: transform, border-radius, box-shadow;
}

.morph-transition::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(120deg, rgba(139, 92, 246, 0.2), rgba(59, 130, 246, 0.2));
  opacity: 0;
  transition: opacity 0.5s ease, transform 0.5s ease;
  border-radius: inherit;
  z-index: 1;
  pointer-events: none;
  transform: scale(0.95);
  will-change: opacity, transform;
}

.morph-transition:hover {
  transform: translateY(-5px);
  border-radius: 24px 12px 24px 12px;
  box-shadow: 0 15px 30px -10px rgba(0, 0, 0, 0.3), 
              0 5px 15px -5px rgba(147, 51, 234, 0.2);
}

.morph-transition:hover::before {
  opacity: 1;
  transform: scale(1);
}

.morph-background {
  background: linear-gradient(135deg, rgba(30, 27, 75, 0.4), rgba(30, 27, 75, 0.1));
  opacity: 0;
  transition: opacity 0.5s ease;
}

.morph-transition:hover .morph-background {
  opacity: 1;
}

/* Ripple Effect - Optimized */
.ripple-effect {
  position: relative;
  overflow: hidden;
  isolation: isolate;
}

.ripple-effect::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0) 60%);
  transform: translate(-50%, -50%) scale(0);
  border-radius: 50%;
  opacity: 0;
  transition: transform 0.8s cubic-bezier(0.22, 1, 0.36, 1), opacity 0.8s ease;
  pointer-events: none;
  will-change: transform, opacity;
}

.ripple-effect:hover::after {
  transform: translate(-50%, -50%) scale(1);
  opacity: 1;
}

.ripple-background {
  background-color: rgba(20, 20, 40, 0.4);
  transition: background-color 0.5s ease;
}

.ripple-effect:hover .ripple-background {
  background-color: rgba(30, 27, 75, 0.6);
}

/* Floating Elements - Optimized */
.floating-elements {
  position: relative;
  overflow: hidden;
}

.floating-elements::before,
.floating-elements::after {
  content: '';
  position: absolute;
  background: radial-gradient(circle, rgba(139, 92, 246, 0.3) 0%, rgba(139, 92, 246, 0) 70%);
  border-radius: 50%;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.4s ease;
  will-change: transform;
}

.floating-elements::before {
  width: 120px;
  height: 120px;
  top: -50px;
  right: -50px;
  animation: float-element 8s ease-in-out infinite;
}

.floating-elements::after {
  width: 100px;
  height: 100px;
  bottom: -40px;
  left: -40px;
  animation: float-element 6s ease-in-out infinite reverse;
  animation-delay: 1s;
}

.floating-elements:hover::before,
.floating-elements:hover::after {
  opacity: 1;
}

.floating-elements-background {
  background: linear-gradient(180deg, rgba(30, 27, 75, 0.2), rgba(20, 20, 40, 0.1));
  transition: opacity 0.5s ease;
  opacity: 0;
}

.floating-elements:hover .floating-elements-background {
  opacity: 1;
}

@keyframes float-element {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg);
  }
  25% {
    transform: translate(10px, -15px) rotate(5deg);
  }
  50% {
    transform: translate(20px, -10px) rotate(10deg);
  }
  75% {
    transform: translate(10px, 5px) rotate(5deg);
  }
}

/* Shimmering Effect - Optimized */
.shimmering {
  position: relative;
  overflow: hidden;
  isolation: isolate;
}

.shimmering::before {
  content: '';
  position: absolute;
  top: 0;
  left: -150%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transform: skewX(-20deg);
  filter: blur(5px);
  will-change: transform;
}

.shimmering:hover::before {
  animation: shimmer-animation 2.5s ease infinite;
}

.shimmering-background {
  background: linear-gradient(135deg, rgba(30, 27, 75, 0.1), rgba(59, 130, 246, 0.1));
  opacity: 0;
  transition: opacity 0.5s ease;
}

.shimmering:hover .shimmering-background {
  opacity: 1;
}

@keyframes shimmer-animation {
  0% {
    left: -150%;
  }
  100% {
    left: 150%;
  }
}

/* Combined Effect Classes */
.combined-effects {
  transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
  will-change: transform, box-shadow;
}

.combined-effects:hover {
  transform: translateY(-7px) scale(1.01);
  box-shadow: 
    0 20px 40px -20px rgba(0, 0, 0, 0.3),
    0 0 15px -5px rgba(147, 51, 234, 0.2);
}

/* Special Effect for Glassmorphism combined with other effects */
.glassmorphism-effect {
  position: relative;
  overflow: hidden;
}

.glassmorphism-effect::before {
  content: '';
  position: absolute;
  inset: 0;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: 1;
  pointer-events: none;
}

.glassmorphism-effect:hover::before {
  opacity: 1;
}

.glassmorphism-background {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  opacity: 0;
  transition: opacity 0.5s ease;
}

.glassmorphism-effect:hover .glassmorphism-background {
  opacity: 1;
}

/* Spotlight Effect - Optimized */
.spotlight-effect {
  position: relative;
  overflow: hidden;
}

.spotlight-effect::before {
  content: '';
  position: absolute;
  width: 150%;
  height: 150%;
  background: radial-gradient(
    circle at center,
    rgba(147, 51, 234, 0.3) 0%,
    transparent 70%
  );
  top: -25%;
  left: -25%;
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none;
  mix-blend-mode: overlay;
  animation: spotlight-move 10s ease-in-out infinite;
  will-change: transform;
}

.spotlight-effect:hover::before {
  opacity: 1;
}

@keyframes spotlight-move {
  0%, 100% { transform: translate(0, 0); }
  25% { transform: translate(5%, -5%); }
  50% { transform: translate(10%, 5%); }
  75% { transform: translate(-5%, 5%); }
}

/* Particle Effect - Optimized */
.particle-effects {
  position: relative;
  overflow: hidden;
}

.particle-background {
  position: relative;
}

.particle-background::before,
.particle-background::after {
  content: '';
  position: absolute;
  background-color: rgba(147, 51, 234, 0.2);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none;
}

.particle-background::before {
  width: 10px;
  height: 10px;
  top: 20%;
  left: 20%;
  animation: particle-float 6s ease-in-out infinite;
}

.particle-background::after {
  width: 15px;
  height: 15px;
  bottom: 20%;
  right: 20%;
  animation: particle-float 8s ease-in-out infinite reverse;
  animation-delay: 1s;
}

.particle-effects:hover .particle-background::before,
.particle-effects:hover .particle-background::after {
  opacity: 0.7;
}

@keyframes particle-float {
  0%, 100% { transform: translate(0, 0) scale(1); }
  50% { transform: translate(20px, -20px) scale(1.2); }
}

/* Additional animation keyframes for visual effects */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes shimmer-effect {
  0% {
    background-position: -500% 0;
  }
  100% {
    background-position: 500% 0;
  }
}

@keyframes morph-bounce {
  0%, 100% {
    border-radius: 60% 40% 30% 70%/60% 30% 70% 40%;
  }
  25% {
    border-radius: 30% 60% 70% 40%/50% 60% 30% 60%;
  }
  50% {
    border-radius: 20% 40% 70% 60%/30% 30% 70% 70%;
  }
  75% {
    border-radius: 40% 60% 30% 70%/60% 40% 50% 40%;
  }
}

/* Float and glow animations now handled by GSAP - see lib/gsap-animations.ts */
/* Use .gsap-float, .gsap-glow-pulse classes */

/* Animation Utility Classes */
.animate-ripple {
  animation: ripple 3s linear infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  background-size: 200% 100%;
  animation: shimmer-effect 8s ease-in-out infinite;
}

.animate-morph-bounce {
  animation: morph-bounce 8s ease-in-out infinite;
}

.animate-glow {
  animation: glow-pulse 2s ease-in-out infinite;
}

.animate-float-smooth {
  animation: float-smooth 6s ease-in-out infinite;
}

/* Animation Timing Utility Classes */
.animation-duration-500 {
  animation-duration: 500ms !important;
}

.animation-duration-1000 {
  animation-duration: 1000ms !important;
}

.animation-duration-2000 {
  animation-duration: 2000ms !important;
}

.animation-duration-3000 {
  animation-duration: 3000ms !important;
}

.animation-duration-5000 {
  animation-duration: 5000ms !important;
}

.animation-duration-8000 {
  animation-duration: 8000ms !important;
}

.animation-duration-10000 {
  animation-duration: 10000ms !important;
}

/* Animation Intensity Classes */
.scale-subtle {
  transform: scale(1.02);
}

.scale-medium {
  transform: scale(1.05);
}

.scale-strong {
  transform: scale(1.08);
}

/* 3D Effect Helper Classes */
.perspective-1000 {
  perspective: 1000px;
}

.transform-style-3d {
  transform-style: preserve-3d;
}

.backface-visibility-hidden {
  backface-visibility: hidden;
}

.will-change-transform {
  will-change: transform;
}

/* Enhanced smooth scrolling */
.smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Apply smooth scrolling to all scrollable containers */
div, section, main, article, aside, nav {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Calendly widget styling */
.calendly-inline-widget {
  min-width: 320px;
  height: 100%;
  position: relative;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Custom styles for Calendly integration */
.calendly-badge-widget {
  left: 20px;
  right: auto;
  position: fixed;
  z-index: 9998;
}

.calendly-badge-content {
  background: linear-gradient(to right, #9333ea, #7e22ce);
  color: white;
  box-shadow: 0 8px 16px rgba(147, 51, 234, 0.3);
  transition: all 0.3s ease;
  padding: 10px 20px;
  border-radius: 50px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.calendly-badge-content:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 20px rgba(147, 51, 234, 0.4);
}

.calendly-overlay {
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
}

.calendly-popup {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  border-radius: 12px;
  overflow: hidden;
}

.calendly-popup .calendly-close-overlay {
  color: white;
}

.calendly-spinner {
  @apply animate-spin h-8 w-8 text-purple-500;
}

/* Fix spacing conflicts with navbar */
.calendly-wrapper {
  margin-top: 2rem;
}

/* Responsive adjustments for Calendly */
@media (max-width: 768px) {
  .calendly-inline-widget {
    height: 500px !important;
  }
  
  .calendly-badge-content {
    padding: 8px 16px;
    font-size: 14px;
  }
}

/* Loading animation for Calendly */
.calendly-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  background-color: #111827;
  color: #9ca3af;
}

.calendly-loading svg {
  margin-bottom: 1rem;
}

/* Modern scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(30, 30, 30, 0.5);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: rgba(147, 51, 234, 0.6);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(147, 51, 234, 0.8);
}

/* Header styling */
.modern-header {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s ease-in-out;
}

.modern-header.scrolled {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
}

/* Modern button effects */
.btn-glow {
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.btn-glow::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to bottom right,
    rgba(147, 51, 234, 0) 0%,
    rgba(147, 51, 234, 0.3) 50%,
    rgba(147, 51, 234, 0) 100%
  );
  transform: rotate(30deg);
  z-index: -1;
  transition: transform 0.5s ease-out;
}

.btn-glow:hover::after {
  transform: rotate(30deg) translate(10%, 10%);
}

/* Calendly animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
  }
  to {
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.3s ease-out forwards;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out forwards;
}

@keyframes gradientMove {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Add shine animation for the review button */
@keyframes shine {
  from {
    left: -100%;
  }
  to {
    left: 200%;
  }
}

.animate-shine {
  animation: shine 1.5s infinite;
}

/* Custom scrollbar for the review modal */
.custom-scrollbar::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(216, 180, 254, 0.05);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(216, 180, 254, 0.3);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(216, 180, 254, 0.5);
}

/* Nexious Chatbot Modern Dark Theme Styling */
/* Enhanced title animation for the NEXIOUS header */
@keyframes nexious-title-glow {
  0%, 100% {
    text-shadow:
      0 0 20px rgba(255, 255, 255, 0.8),
      0 0 40px rgba(147, 51, 234, 0.4),
      0 0 60px rgba(79, 70, 229, 0.3);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.4)) brightness(1);
  }
  50% {
    text-shadow:
      0 0 30px rgba(255, 255, 255, 1),
      0 0 60px rgba(147, 51, 234, 0.6),
      0 0 90px rgba(79, 70, 229, 0.4);
    filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.5)) brightness(1.1);
  }
}

@keyframes nexious-gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.nexious-title-animation {
  animation: nexious-title-glow 4s ease-in-out infinite;
  letter-spacing: -0.01em;
  background-size: 200% 200%;
  animation: nexious-title-glow 4s ease-in-out infinite, nexious-gradient-shift 6s ease-in-out infinite;
}

.nexious-title-modern {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
  font-weight: 900;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 20%, #e2e8f0 40%, #cbd5e1 60%, #8b5cf6 80%, #7c3aed 100%);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: none;
}

/* Enhanced dark theme for chatbot components */
.nexious-dark-theme {
  background: linear-gradient(135deg, rgba(17, 24, 39, 0.98) 0%, rgba(31, 41, 55, 0.98) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(75, 85, 99, 0.3);
}

.nexious-message-dark {
  background: rgba(31, 41, 55, 0.9);
  border: 1px solid rgba(75, 85, 99, 0.2);
  backdrop-filter: blur(8px);
}

.nexious-input-dark {
  background: rgba(31, 41, 55, 0.8);
  border: 1px solid rgba(75, 85, 99, 0.4);
  backdrop-filter: blur(8px);
  transition: border-color 0.3s ease, background-color 0.3s ease;
}

.nexious-input-dark:focus {
  border-color: rgba(147, 51, 234, 0.5);
  background: rgba(31, 41, 55, 0.95);
  box-shadow: 0 0 0 3px rgba(147, 51, 234, 0.1);
}

/* Enhanced AI Robot Typing Indicator Animations */
@keyframes ai-robot-thinking {
  0%, 100% {
    transform: scale(1) rotate(0deg);
  }
  25% {
    transform: scale(1.05) rotate(1deg);
  }
  50% {
    transform: scale(1.1) rotate(0deg);
  }
  75% {
    transform: scale(1.05) rotate(-1deg);
  }
}

@keyframes robot-eye-blink {
  0%, 90%, 100% {
    opacity: 1;
    transform: scaleY(1);
  }
  95% {
    opacity: 0.3;
    transform: scaleY(0.1);
  }
}

@keyframes robot-signal-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.5);
  }
}

@keyframes processing-dots {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

.ai-robot-thinking {
  animation: ai-robot-thinking 2s ease-in-out infinite;
}

.robot-eye-left {
  animation: robot-eye-blink 3s ease-in-out infinite;
}

.robot-eye-right {
  animation: robot-eye-blink 3s ease-in-out infinite 0.5s;
}

.robot-signal {
  animation: robot-signal-pulse 1.5s ease-in-out infinite;
}

.processing-dot-1 {
  animation: processing-dots 1s ease-in-out infinite;
}

.processing-dot-2 {
  animation: processing-dots 1s ease-in-out infinite 0.3s;
}

/* Professional thinking pulse animation */
.thinking-pulse {
  animation: thinking-glow 2s ease-in-out infinite;
}

@keyframes thinking-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(147, 51, 234, 0.2);
  }
  50% {
    box-shadow: 0 0 30px rgba(147, 51, 234, 0.4);
  }
}

/* Animated particles background for chat header */
.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.particles-container::before,
.particles-container::after {
  content: '';
  position: absolute;
  width: 3px;
  height: 3px;
  background: white;
  border-radius: 50%;
  opacity: 0.4;
}

.particles-container::before {
  top: 20%;
  left: 10%;
  animation: particle-float 6s ease-in-out infinite;
}

.particles-container::after {
  top: 70%;
  left: 60%;
  width: 2px;
  height: 2px;
  animation: particle-float 8s ease-in-out infinite 2s;
}

/* PRO MODE button styling */
.pro-mode-btn {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  z-index: 1;
}

.pro-mode-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.6s ease;
  z-index: -1;
}

.pro-mode-btn:hover::before {
  left: 100%;
}

/* Animated logo pulse effect */
.logo-pulse-container {
  position: relative;
  overflow: hidden;
}

.logo-pulse-container::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(139, 92, 246, 0.6) 0%, transparent 70%);
  border-radius: 50%;
  animation: logo-pulse 3s ease-in-out infinite;
  z-index: 0;
}

@keyframes logo-pulse {
  0%, 100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(0.8);
  }
  50% {
    opacity: 0.5;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

/* Message animation effects */
@keyframes message-slide-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-slide-in {
  animation: message-slide-in 0.3s ease-out forwards;
}

.user-message {
  animation-delay: 0.05s;
}

.assistant-message {
  animation-delay: 0.1s;
}

/* Smooth chat open/close transitions */
@keyframes material-slideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.96);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes material-slideUp {
  from {
    opacity: 0;
    transform: translateY(15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-material-slideIn {
  animation: material-slideIn 0.3s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

.animate-material-slideUp {
  animation: material-slideUp 0.25s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

/* Header components animation */
@keyframes header-component-reveal {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.header-component {
  animation: header-component-reveal 0.4s ease-out forwards;
}

.header-component:nth-child(1) {
  animation-delay: 0.05s;
}

.header-component:nth-child(2) {
  animation-delay: 0.1s;
}

.header-component:nth-child(3) {
  animation-delay: 0.15s;
}

/* PRO mode specific styling */
.pro-mode-active {
  --pro-glow-color: rgba(37, 99, 235, 0.25);
  box-shadow: 0 0 15px var(--pro-glow-color), 0 0 25px var(--pro-glow-color);
}

.pro-mode-header {
  position: relative;
  background: linear-gradient(to right, #0a0a0a, #1a1a1a);
  overflow: hidden;
}

.pro-mode-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(90deg, 
      transparent 0%,
      rgba(32, 196, 253, 0.1) 50%,
      transparent 100%),
    repeating-linear-gradient(
      transparent,
      transparent 20px,
      rgba(32, 196, 253, 0.05) 20px,
      rgba(32, 196, 253, 0.05) 21px
    );
  background-size: 200% 100%, 100% 100%;
  animation: code-flow 8s linear infinite;
  pointer-events: none;
}

.pro-mode-header::after {
  content: '01';
  position: absolute;
  font-family: monospace;
  font-size: 0.7rem;
  color: rgba(32, 196, 253, 0.2);
  white-space: nowrap;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-30deg);
  animation: code-fade 4s ease-in-out infinite;
  pointer-events: none;
}

@keyframes code-flow {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 200% 0%;
  }
}

@keyframes code-fade {
  0%, 100% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.5;
  }
}

/* Animation for typing dots */
@keyframes typing-dot {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-3px);
  }
}

.animate-typing-dot {
  animation: typing-dot 1.4s infinite ease-in-out;
}

.animate-typing-dot:nth-child(1) {
  animation-delay: 0s;
}

.animate-typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.animate-typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

/* Animation for send button */
@keyframes slight-bounce {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.animate-slight-bounce {
  animation: slight-bounce 0.6s ease-in-out;
}

/* Text gradient animation */
.text-gradient {
  background: linear-gradient(45deg, #3b82f6, #8b5cf6, #ec4899);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  background-size: 200% 200%;
  animation: gradient-animation 3s ease infinite;
}

@keyframes gradient-animation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Enhanced chatbot sidebar styling */
.chatbot-sidebar {
  width: 70px;
  transition: transform 0.4s cubic-bezier(0.16, 1, 0.3, 1), opacity 0.3s ease, box-shadow 0.4s ease;
  box-shadow: -8px 0 32px rgba(0, 0, 0, 0.6), -4px 0 16px rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(24px);
  border-left: 1px solid rgba(75, 85, 99, 0.3);
  will-change: transform, opacity, width;
}

.chatbot-sidebar.hidden {
  transform: translateX(120%);
  opacity: 0;
  transition: transform 0.5s cubic-bezier(0.65, 0, 0.35, 1), opacity 0.4s ease;
}

/* Sidebar Panels Animation - Improved */
.sidebar-panel {
  animation: sidebar-panel-enter 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  transform-origin: right center;
}

@keyframes sidebar-panel-enter {
  from {
    opacity: 0;
    transform: translateX(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* Temperature slider styling - more sleek */
.temperature-slider {
  position: relative;
  transition: all 0.25s cubic-bezier(0.25, 1, 0.5, 1);
  will-change: transform, background-color;
  border-radius: 999px;
  box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(4px);
  padding: 1px;
  overflow: hidden;
}

.temperature-slider::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(to bottom, rgba(255,255,255,0.1), transparent);
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.temperature-slider:hover::before {
  opacity: 1;
}

.temperature-slider:hover {
  background-color: #2d3748;
  transform: scale(1.03);
  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.4);
}

.slider-track {
  transition: height 0.25s cubic-bezier(0.25, 1, 0.5, 1);
  border-top-left-radius: 999px;
  border-top-right-radius: 999px;
  will-change: height;
  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.15);
}

/* Improved slider thumb */
.slider-thumb {
  transition: bottom 0.25s cubic-bezier(0.25, 1, 0.5, 1), transform 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  z-index: 2;
  will-change: transform, bottom, box-shadow;
  border: 1.5px solid #e5e7eb;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.25);
}

.temperature-slider:hover .slider-thumb {
  transform: scale(1.15);
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.6);
  border-color: white;
  animation: pulse-glow 2s infinite ease-in-out;
}

.temperature-slider:active .slider-thumb {
  transform: scale(1.25);
  transition: transform 0.1s cubic-bezier(0.25, 1, 0.5, 1);
}

.slider-label {
  font-weight: 500;
  letter-spacing: 0.5px;
  transition: opacity 0.2s ease, transform 0.2s ease;
  font-size: 10px;
  color: #a0aec0;
}

.temperature-slider:hover + .slider-label {
  transform: translateY(2px) scale(1.05);
  color: #e2e8f0;
}

/* Sidebar buttons hover effect */
.sidebar-btn {
  position: relative;
  overflow: hidden;
  transform: translateZ(0);
  transition: all 0.25s cubic-bezier(0.25, 1, 0.5, 1);
  will-change: transform, background-color;
}

.sidebar-btn:hover {
  transform: translateY(-2px);
}

.sidebar-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 100%;
  transform: translate(-50%, -50%);
  transition: width 0.5s cubic-bezier(0.25, 1, 0.5, 1), height 0.5s cubic-bezier(0.25, 1, 0.5, 1);
  will-change: width, height;
}

.sidebar-btn:hover::after {
  width: 225%;
  height: 225%;
}

/* Divider styling */
.chatbot-sidebar .w-10.h-px {
  background: linear-gradient(to right, transparent, rgba(156, 163, 175, 0.5), transparent);
  transition: opacity 0.3s ease;
}

/* Chatbot sidebar responsiveness - improved */
@media (max-width: 640px) {
  .chatbot-sidebar {
    width: 75px !important;
    box-shadow: -5px 0 25px rgba(0, 0, 0, 0.4);
  }

  body.sidebar-open {
    overflow: hidden;
    position: fixed;
    width: 100%;
    height: 100%;
  }

  .sidebar-btn {
    transform: scale(1);
    min-height: 48px !important;
    min-width: 48px !important;
  }

  .temperature-slider {
    height: 24vh !important;
    min-height: 120px;
  }
  
  /* Add mobile backdrop when sidebar is open */
  .mobile-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0);
    backdrop-filter: blur(0px);
    z-index: 9;
    opacity: 0;
    transition: opacity 0.4s ease, backdrop-filter 0.4s ease;
    pointer-events: none;
  }
  
  .mobile-backdrop.active {
    opacity: 1;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(3px);
    pointer-events: auto;
  }
  
  /* Slide chat container when sidebar is open */
  .nexious-chat-container {
    transition: transform 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  }
  
  body.sidebar-open .nexious-chat-container {
    transform: translateX(-30px);
  }
}

/* Enhanced responsive slider and mobile optimizations */
@media (max-width: 768px) {
  .chatbot-sidebar {
    width: 95px !important;
  }

  .slider-thumb {
    transform: scale(1.1);
    width: 20px !important;
    height: 20px !important;
    font-size: 10px !important;
    font-weight: 600 !important;
  }

  .sidebar-panel {
    gap: 1rem;
  }

  /* Enhanced touch targets for mobile */
  .sidebar-btn {
    min-height: 52px !important;
    min-width: 52px !important;
    border-radius: 16px !important;
  }

  /* Mobile chatbot button optimizations */
  .nexious-chat-container button {
    min-height: 48px !important;
    min-width: 48px !important;
  }

  /* Enhanced suggested questions for mobile */
  .nexious-chat button[class*="suggested"] {
    min-height: 52px !important;
    font-size: 14px !important;
    padding: 16px 20px !important;
    border-radius: 16px !important;
  }

  /* Mobile input optimizations */
  .nexious-chat textarea {
    font-size: 17px !important; /* Prevent zoom on iOS */
    line-height: 1.4 !important;
  }

  /* Mobile send button */
  .nexious-chat button[aria-label="Send message"] {
    min-height: 52px !important;
    min-width: 52px !important;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .chatbot-sidebar {
    width: 70px !important;
  }

  .sidebar-panel {
    padding: 0.5rem !important;
  }

  .temperature-slider {
    height: 20vh !important;
    width: 8px !important;
  }

  .slider-thumb {
    width: 16px !important;
    height: 16px !important;
    font-size: 8px !important;
    transform: scale(1);
  }

  /* Ensure minimum touch target size */
  .sidebar-btn {
    min-height: 44px !important;
    min-width: 44px !important;
  }
}

/* Small height screens - adjust slider heights */
@media (max-height: 700px) {
  .temperature-slider {
    height: 18vh !important;
  }
  
  .sidebar-panel {
    gap: 0.5rem !important;
  }
}

/* Active slider state for better touch feedback */
.temperature-slider:active {
  background-color: #3a4657;
  transform: scale(1.05);
}

/* Pro Mode popup mobile optimizations */
@media (max-width: 640px) {
  .pro-features-popup {
    width: calc(100% - 20px) !important;
    max-width: 85vw !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    bottom: 90px !important;
  }

  .pro-features-popup .p-3 {
    padding: 1rem !important;
  }

  .pro-features-popup h4 {
    font-size: 0.9rem !important;
  }

  .pro-features-popup p {
    font-size: 0.8rem !important;
  }
}

/* Suggested questions mobile improvements */
@media (max-width: 640px) {
  .material-button-subtle {
    min-height: 44px !important;
    padding: 0.75rem 1rem !important;
    font-size: 0.8rem !important;
    touch-action: manipulation;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  .material-button-subtle:active {
    transform: scale(0.95) !important;
  }
}

/* Enhanced chatbot container mobile stability and optimization */
@media (max-width: 640px) {
  #chat-window {
    min-height: 580px !important;
    max-height: 92vh !important;
    border-radius: 24px !important;
    margin: 0 4px !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8), 0 0 0 1px rgba(255, 255, 255, 0.1) !important;
  }

  #nexious-chat-container {
    transition: transform 0.3s ease, width 0.3s ease, height 0.3s ease;
    padding: 0 4px !important;
  }

  /* Prevent container expansion during response generation */
  .chat-messages {
    overflow-y: auto;
    max-height: calc(92vh - 220px);
  }

  /* Enhanced mobile touch feedback for all buttons */
  .sidebar-btn:active,
  .nexious-chat-container button:active,
  button:active {
    transform: scale(0.92) !important;
    background-color: rgba(147, 51, 234, 0.4) !important;
    transition: all 0.1s ease !important;
  }

  /* Better mobile backdrop */
  .mobile-backdrop.active {
    backdrop-filter: blur(6px) !important;
    background-color: rgba(0, 0, 0, 0.7) !important;
  }

  /* Enhanced suggested questions mobile styling */
  .nexious-chat button[class*="bg-gray-700"] {
    min-height: 52px !important;
    padding: 16px 20px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    border-radius: 16px !important;
    border: 1px solid rgba(99, 102, 241, 0.2) !important;
    min-width: 120px !important;
  }

  .nexious-chat button[class*="bg-gray-700"]:active {
    background-color: rgba(99, 102, 241, 0.3) !important;
    transform: scale(0.95) !important;
  }

  /* Mobile input optimizations */
  .nexious-chat textarea {
    font-size: 17px !important; /* Prevent zoom on iOS */
    line-height: 1.4 !important;
    min-height: 56px !important;
  }

  /* Mobile send button enhancements */
  .nexious-chat button[aria-label="Send message"],
  .nexious-chat button[aria-label="Pause generation"] {
    min-height: 52px !important;
    min-width: 52px !important;
    border-radius: 50% !important;
  }

  /* Improved mobile popup positioning */
  .pro-features-popup,
  .pro-maintenance-popup {
    animation: slideUpMobile 0.3s ease-out !important;
  }
}

@keyframes slideUpMobile {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* Enhanced touch interaction for all mobile elements */
@media (max-width: 640px) {
  .material-button-subtle:active,
  .sidebar-btn:active,
  button:active {
    transform: scale(0.92) !important;
    transition: transform 0.1s ease !important;
  }

  /* Prevent text selection on touch elements */
  .sidebar-btn,
  .material-button-subtle,
  .pro-features-popup,
  .pro-maintenance-popup,
  .nexious-chat-container button {
    -webkit-touch-callout: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    touch-action: manipulation !important;
  }

  /* Enhanced mobile button styling */
  .nexious-chat-container button {
    min-height: 48px !important;
    min-width: 48px !important;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  }

  /* Mobile header buttons optimization */
  .nexious-chat-container .mobile-header button {
    min-height: 52px !important;
    min-width: 52px !important;
    border-radius: 16px !important;
  }

  /* Mobile PRO mode button enhancement */
  .nexious-chat-container button[aria-label*="PRO"] {
    min-height: 48px !important;
    min-width: 48px !important;
    font-size: 12px !important;
    font-weight: 600 !important;
  }
}

/* Slider animation */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 12px rgba(59, 130, 246, 0.6);
  }
}

/* Highlight pulse animation for RequirementsModal */
@keyframes highlight-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 20px 5px rgba(147, 51, 234, 0.4);
    background-color: rgba(147, 51, 234, 0.05);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 0 30px 10px rgba(147, 51, 234, 0.7);
    background-color: rgba(147, 51, 234, 0.1);
  }
}

.highlight-pulse {
  animation: highlight-pulse 2s infinite ease-in-out;
  position: relative;
  z-index: 1000 !important;
  border-radius: 16px;
  border: 2px solid rgba(147, 51, 234, 0.6) !important;
}

/* Custom scrollbar for RequirementsModal */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-purple-500\/30::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thumb-purple-500\/30::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thumb-purple-500\/30::-webkit-scrollbar-thumb {
  background-color: rgba(147, 51, 234, 0.3);
  border-radius: 3px;
}

.scrollbar-thumb-purple-500\/30::-webkit-scrollbar-thumb:hover {
  background-color: rgba(147, 51, 234, 0.5);
}

.scrollbar-track-transparent::-webkit-scrollbar-track {
  background: transparent;
}

/* Chatbot Sidebar Styles */
.chatbot-sidebar {
  width: 70px;
  transition: transform 0.4s cubic-bezier(0.16, 1, 0.3, 1), opacity 0.3s ease, box-shadow 0.4s ease;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
  will-change: transform, opacity, width;
}

.chatbot-sidebar.hidden {
  transform: translateX(120%);
  opacity: 0;
  transition: transform 0.5s cubic-bezier(0.65, 0, 0.35, 1), opacity 0.4s ease;
}

/* Mobile optimization for sidebar */
@media (max-width: 640px) {
  .chatbot-sidebar {
    width: 60px;
    box-shadow: -5px 0 25px rgba(0, 0, 0, 0.4);
  }
  
  body.sidebar-open {
    overflow: hidden;
    position: fixed;
    width: 100%;
    height: 100%;
  }
  
  .sidebar-btn {
    transform: scale(0.95);
  }
  
  .temperature-slider {
    height: 24vh !important;
    min-height: 120px;
  }
  
  /* Add mobile backdrop when sidebar is open */
  .mobile-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0);
    backdrop-filter: blur(0px);
    z-index: 9;
    opacity: 0;
    transition: opacity 0.4s ease, backdrop-filter 0.4s ease;
    pointer-events: none;
  }
  
  .mobile-backdrop.active {
    opacity: 1;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(3px);
    pointer-events: auto;
  }
  
  /* Slide chat container when sidebar is open */
  .nexious-chat-container {
    transition: transform 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  }
  
  body.sidebar-open .nexious-chat-container {
    transform: translateX(-30px);
  }
}

/* Sidebar panel transitions */
.sidebar-panel {
  animation: sidebar-panel-enter 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  transform-origin: right center;
}

@keyframes sidebar-panel-enter {
  from {
    opacity: 0;
    transform: translateX(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* Slider styles */
.temperature-slider {
  transition: background-color 0.2s ease;
}

.temperature-slider:hover {
  background-color: rgba(31, 41, 55, 0.8);
}

.slider-track {
  transition: height 0.2s ease;
}

.slider-thumb {
  transition: bottom 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;
}

.slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

/* Button hover effects */
.sidebar-btn {
  transition: transform 0.2s ease, background-color 0.2s ease, color 0.2s ease;
}

.sidebar-btn:hover {
  transform: translateY(-2px);
}

.sidebar-btn:active {
  transform: translateY(0);
}

@keyframes gradient-x {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.animate-gradient-x {
  animation: gradient-x 15s ease infinite;
  background-size: 200% 200%;
}

/* Enable smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Optimize for animations */
* {
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Prevent layout shifts during animations */
.prevent-layout-shift {
  transform: translateZ(0);
  will-change: transform;
}

/* Enhanced AI Model Info Connecting Lines Animation - 60fps Optimized */
@keyframes drawLineTopEnhanced {
  0% {
    stroke-dashoffset: 140;
    opacity: 0;
    filter: drop-shadow(0 0 0px rgba(139, 92, 246, 0));
    transform: translateZ(0);
  }
  20% {
    opacity: 0.6;
    filter: drop-shadow(0 0 4px rgba(139, 92, 246, 0.6));
  }
  50% {
    opacity: 0.9;
    filter: drop-shadow(0 0 8px rgba(139, 92, 246, 0.8)) drop-shadow(0 0 16px rgba(139, 92, 246, 0.4));
  }
  100% {
    stroke-dashoffset: 0;
    opacity: 1;
    filter: drop-shadow(0 0 8px rgba(139, 92, 246, 0.8)) drop-shadow(0 0 16px rgba(139, 92, 246, 0.4));
    transform: translateZ(0);
  }
}

@keyframes drawLineBottomEnhanced {
  0% {
    stroke-dashoffset: 160;
    opacity: 0;
    filter: drop-shadow(0 0 0px rgba(59, 130, 246, 0));
    transform: translateZ(0);
  }
  25% {
    opacity: 0.7;
    filter: drop-shadow(0 0 6px rgba(59, 130, 246, 0.7));
  }
  60% {
    opacity: 0.95;
    filter: drop-shadow(0 0 10px rgba(59, 130, 246, 0.9)) drop-shadow(0 0 20px rgba(59, 130, 246, 0.5));
  }
  100% {
    stroke-dashoffset: 0;
    opacity: 1;
    filter: drop-shadow(0 0 10px rgba(59, 130, 246, 0.9)) drop-shadow(0 0 20px rgba(59, 130, 246, 0.5));
    transform: translateZ(0);
  }
}



.animate-draw-line-top-enhanced {
  animation: drawLineTopEnhanced 1.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s forwards;
  will-change: stroke-dashoffset, opacity, filter;
  backface-visibility: hidden;
  perspective: 1000px;
}

.animate-draw-line-bottom-enhanced {
  animation: drawLineBottomEnhanced 2.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.6s forwards;
  will-change: stroke-dashoffset, opacity, filter;
  backface-visibility: hidden;
  perspective: 1000px;
}



/* Welcome Screen Forceful Positioning */
.welcome-screen-container {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 99999 !important;
  overflow: hidden !important;
  overscroll-behavior: none !important;
  touch-action: none !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  pointer-events: auto !important;
}

/* Welcome Screen Header Text Visibility */
.welcome-header-text {
  position: relative !important;
  z-index: 100000 !important;
  text-shadow:
    0 0 30px rgba(96, 165, 250, 1),
    0 0 60px rgba(167, 139, 250, 0.8),
    0 0 90px rgba(52, 211, 153, 0.6),
    0 4px 8px rgba(0, 0, 0, 1),
    0 0 20px rgba(96, 165, 250, 0.8) !important;
  filter:
    drop-shadow(0 4px 8px rgba(0, 0, 0, 1))
    drop-shadow(0 0 20px rgba(96, 165, 250, 0.8))
    drop-shadow(0 0 40px rgba(167, 139, 250, 0.6)) !important;
}

/* Optimize scrolling performance */
body {
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior-y: none;
}

/* Enhanced mobile animations and transitions */
@media (max-width: 640px) {
  #nexious-chat-container {
    transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1), 
                opacity 0.25s cubic-bezier(0.4, 0, 0.2, 1), 
                width 0.25s cubic-bezier(0.4, 0, 0.2, 1), 
                height 0.25s cubic-bezier(0.4, 0, 0.2, 1) !important;
    will-change: transform, opacity;
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  .nexious-chat-container button {
    transition: transform 0.15s cubic-bezier(0.4, 0, 0.2, 1),
                background-color 0.15s cubic-bezier(0.4, 0, 0.2, 1) !important;
    will-change: transform, background-color;
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  /* Optimize mobile backdrop transitions */
  .mobile-backdrop {
    transition: opacity 0.25s cubic-bezier(0.4, 0, 0.2, 1),
                backdrop-filter 0.25s cubic-bezier(0.4, 0, 0.2, 1) !important;
    will-change: opacity, backdrop-filter;
  }

  /* Optimize sidebar transitions */
  .chatbot-sidebar {
    transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1),
                opacity 0.25s cubic-bezier(0.4, 0, 0.2, 1) !important;
    will-change: transform, opacity;
    transform: translateZ(0);
    backface-visibility: hidden;
  }
}

/* Optimized mobile header buttons */
@media (max-width: 640px) {
  .nexious-chat-container .mobile-header button {
    min-height: 32px !important;
    min-width: 32px !important;
    width: 32px !important;
    height: 32px !important;
    border-radius: 10px !important;
    padding: 6px !important;
    margin: 0 2px !important;
  }

  .nexious-chat-container .mobile-header {
    padding: 8px 12px !important;
    height: auto !important;
    min-height: 48px !important;
  }

  .nexious-chat-container .mobile-header svg {
    width: 16px !important;
    height: 16px !important;
  }

  /* Adjust spacing for mobile header elements */
  .nexious-chat-container .mobile-header > div {
    gap: 4px !important;
  }
}

/* Enhanced mobile responsiveness */
@media (max-width: 640px) {
  #chat-window {
    min-height: 520px !important;
    max-height: 85vh !important;
    margin: 0 6px !important;
    border-radius: 20px !important;
    box-shadow: 0 12px 36px rgba(0, 0, 0, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.08) !important;
  }

  .chat-messages {
    overflow-y: auto;
    max-height: calc(85vh - 180px) !important;
    padding: 12px !important;
  }

  /* Optimize touch targets */
  .nexious-chat-container button:not(.mobile-header button) {
    min-height: 44px !important;
    min-width: 44px !important;
    margin: 2px !important;
  }

  /* Improve mobile input area */
  .nexious-chat textarea {
    padding: 12px !important;
    font-size: 16px !important;
    line-height: 1.4 !important;
    min-height: 48px !important;
    max-height: 120px !important;
  }

  /* Optimize mobile send button */
  .nexious-chat button[aria-label="Send message"] {
    min-height: 44px !important;
    min-width: 44px !important;
    width: 44px !important;
    height: 44px !important;
    border-radius: 12px !important;
    margin-left: 8px !important;
  }

  /* Adjust mobile chat container positioning */
  #nexious-chat-container {
    bottom: 8px !important;
    left: 8px !important;
    right: 8px !important;
    margin: 0 !important;
  }
}

/* Enhanced mobile chat interface */
@media (max-width: 640px) {
  /* Optimize send button size */
  .nexious-chat button[aria-label="Send message"] {
    min-height: 36px !important;
    min-width: 36px !important;
    width: 36px !important;
    height: 36px !important;
    border-radius: 10px !important;
    margin-left: 6px !important;
  }

  .nexious-chat button[aria-label="Send message"] svg {
    width: 16px !important;
    height: 16px !important;
  }

  /* Optimize chat message boxes */
  .chat-messages .message {
    padding: 8px 12px !important;
    margin: 4px 0 !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
    border-radius: 12px !important;
  }

  .chat-messages .message.assistant {
    margin-right: 24px !important;
  }

  .chat-messages .message.user {
    margin-left: 24px !important;
  }

  /* Adjust top header area */
  .nexious-chat-container .mobile-header {
    height: 42px !important;
    padding: 6px 10px !important;
    margin-bottom: 0 !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  }

  /* Header button borders */
  .nexious-chat-container .mobile-header button {
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    background: rgba(255, 255, 255, 0.05) !important;
  }

  /* Fix chatbot positioning */
  #nexious-chat-container {
    bottom: 0 !important;
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
  }

  #chat-window {
    margin-bottom: 0 !important;
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
    max-height: calc(100vh - 60px) !important; /* Adjust for nav bar */
    height: calc(100vh - 60px) !important;
  }

  /* Adjust suggested questions size */
  .nexious-chat button[class*="bg-gray-700"] {
    font-size: 13px !important;
    padding: 10px 16px !important;
    min-height: 40px !important;
  }

  /* Adjust input area */
  .nexious-chat textarea {
    font-size: 14px !important;
    padding: 8px 12px !important;
    min-height: 42px !important;
  }

  /* Status text and counters */
  .nexious-chat .text-xs,
  .nexious-chat .text-sm {
    font-size: 12px !important;
  }

  /* Pro badge and status indicators */
  .nexious-chat .pro-badge,
  .nexious-chat .status-indicator {
    font-size: 11px !important;
    padding: 2px 6px !important;
  }
}
