'use client';

import { useState, useEffect } from 'react';
import GSAP60FPSOptimizer from '@/lib/gsap-60fps-optimizer';
import GSAPTestingSuite from '@/lib/gsap-testing-suite';
import GSAPChatbotProtection from '@/lib/gsap-chatbot-protection';
import GSAPMemoryOptimizer from '@/lib/gsap-memory-optimizer';

interface PerformanceStats {
  fps: number;
  performanceMode: string;
  activeAnimations: number;
  protectedElements: number;
  memoryUsage?: number;
}

export default function GSAPPerformanceMonitor() {
  const [stats, setStats] = useState<PerformanceStats>({
    fps: 60,
    performanceMode: 'high',
    activeAnimations: 0,
    protectedElements: 0
  });
  const [isVisible, setIsVisible] = useState(false);
  const [testResults, setTestResults] = useState<any>(null);
  const [isRunningTests, setIsRunningTests] = useState(false);

  // Only show in development
  useEffect(() => {
    setIsVisible(process.env.NODE_ENV === 'development');
  }, []);

  // Update stats every second
  useEffect(() => {
    if (!isVisible) return;

    const updateStats = () => {
      const perfStats = GSAP60FPSOptimizer.getPerformanceStats();
      const protectedElements = GSAPChatbotProtection.getProtectedElements().length;
      const memoryStats = GSAPMemoryOptimizer.getMemoryStats();

      setStats({
        fps: perfStats.actualFPS,
        performanceMode: perfStats.performanceMode,
        activeAnimations: memoryStats.activeAnimations,
        protectedElements,
        memoryUsage: memoryStats.memoryUsage?.used
      });
    };

    updateStats();
    const interval = setInterval(updateStats, 1000);

    return () => clearInterval(interval);
  }, [isVisible]);

  // Run comprehensive tests
  const runTests = async () => {
    setIsRunningTests(true);
    try {
      const results = await GSAPTestingSuite.runAllTests();
      setTestResults(results);
    } catch (error) {
      console.error('Test failed:', error);
    } finally {
      setIsRunningTests(false);
    }
  };

  // Get status color based on performance
  const getStatusColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value >= thresholds.good) return 'text-green-400';
    if (value >= thresholds.warning) return 'text-yellow-400';
    return 'text-red-400';
  };

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-4 right-4 z-[9999] bg-black/90 backdrop-blur-sm border border-white/20 rounded-lg p-4 text-white text-xs font-mono max-w-sm">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-bold text-purple-400">GSAP Monitor</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-white"
        >
          ×
        </button>
      </div>

      {/* Performance Stats */}
      <div className="space-y-2 mb-4">
        <div className="flex justify-between">
          <span>FPS:</span>
          <span className={getStatusColor(stats.fps, { good: 55, warning: 30 })}>
            {stats.fps}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>Mode:</span>
          <span className={
            stats.performanceMode === 'high' ? 'text-green-400' :
            stats.performanceMode === 'medium' ? 'text-yellow-400' : 'text-red-400'
          }>
            {stats.performanceMode}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>Animations:</span>
          <span className={getStatusColor(10 - stats.activeAnimations, { good: 5, warning: 2 })}>
            {stats.activeAnimations}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>Protected:</span>
          <span className="text-blue-400">{stats.protectedElements}</span>
        </div>
        
        {stats.memoryUsage && (
          <div className="flex justify-between">
            <span>Memory:</span>
            <span className={getStatusColor(100 - stats.memoryUsage, { good: 50, warning: 20 })}>
              {stats.memoryUsage}MB
            </span>
          </div>
        )}
      </div>

      {/* Test Controls */}
      <div className="border-t border-white/20 pt-3">
        <button
          onClick={runTests}
          disabled={isRunningTests}
          className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 px-3 py-2 rounded text-xs font-medium transition-colors"
        >
          {isRunningTests ? 'Running Tests...' : 'Run Tests'}
        </button>
        
        {testResults && (
          <div className="mt-3 p-2 bg-black/50 rounded text-xs">
            <div className="flex justify-between mb-2">
              <span>Results:</span>
              <span className={testResults.summary.success ? 'text-green-400' : 'text-red-400'}>
                {testResults.summary.passed}/{testResults.summary.total}
              </span>
            </div>
            
            {testResults.summary.failed > 0 && (
              <div className="text-red-400 mb-1">
                ❌ {testResults.summary.failed} failed
              </div>
            )}
            
            {testResults.summary.warnings > 0 && (
              <div className="text-yellow-400 mb-1">
                ⚠️ {testResults.summary.warnings} warnings
              </div>
            )}
            
            {testResults.recommendations.length > 0 && (
              <div className="mt-2 text-xs text-gray-300">
                {testResults.recommendations[0]}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="border-t border-white/20 pt-3 mt-3">
        <div className="flex gap-2">
          <button
            onClick={() => GSAP60FPSOptimizer.setPerformanceMode('high')}
            className="flex-1 bg-green-600/20 hover:bg-green-600/30 px-2 py-1 rounded text-xs"
          >
            High
          </button>
          <button
            onClick={() => GSAP60FPSOptimizer.setPerformanceMode('medium')}
            className="flex-1 bg-yellow-600/20 hover:bg-yellow-600/30 px-2 py-1 rounded text-xs"
          >
            Med
          </button>
          <button
            onClick={() => GSAP60FPSOptimizer.setPerformanceMode('low')}
            className="flex-1 bg-red-600/20 hover:bg-red-600/30 px-2 py-1 rounded text-xs"
          >
            Low
          </button>
        </div>
      </div>
    </div>
  );
}
