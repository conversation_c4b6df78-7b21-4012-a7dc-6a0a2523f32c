'use client';

import { gsap } from 'gsap';

// 60fps GSAP Optimizer - Ensures smooth animations at 60fps
export class GSAP60FPSOptimizer {
  private static initialized = false;
  private static performanceMode: 'high' | 'medium' | 'low' = 'high';
  private static frameRate = 60;
  private static lastFrameTime = 0;
  private static frameCount = 0;
  private static actualFPS = 60;

  // Initialize 60fps optimizations
  static init() {
    if (this.initialized || typeof window === 'undefined') return;

    // Configure GSAP for 60fps performance
    gsap.config({
      force3D: true,
      nullTargetWarn: false,
      trialWarn: false,
      autoSleep: 60, // Sleep after 60 seconds of inactivity
    });

    // Set 60fps defaults
    gsap.defaults({
      duration: 0.5,
      ease: "power2.out",
      force3D: true,
      lazy: false,
    });

    // Setup performance monitoring
    this.setupFPSMonitoring();

    // Setup adaptive quality
    this.setupAdaptiveQuality();

    // Optimize for device capabilities
    this.optimizeForDevice();

    // Apply professional optimizations
    this.applyProfessionalOptimizations();

    // Setup smooth scrolling
    this.setupSmoothScrolling();

    // Initialize animation batching
    this.initAnimationBatching();

    this.initialized = true;
  }

  // Monitor actual FPS and adjust accordingly
  private static setupFPSMonitoring() {
    const measureFPS = (currentTime: number) => {
      this.frameCount++;
      
      if (currentTime - this.lastFrameTime >= 1000) {
        this.actualFPS = Math.round((this.frameCount * 1000) / (currentTime - this.lastFrameTime));
        this.frameCount = 0;
        this.lastFrameTime = currentTime;
        
        // Adjust performance mode based on FPS
        this.adjustPerformanceMode();
      }
      
      requestAnimationFrame(measureFPS);
    };
    
    requestAnimationFrame(measureFPS);
  }

  // Adjust performance mode based on actual FPS
  private static adjustPerformanceMode() {
    if (this.actualFPS < 30) {
      this.performanceMode = 'low';
      gsap.globalTimeline.timeScale(0.7);
      console.warn('Low FPS detected, switching to low performance mode');
    } else if (this.actualFPS < 45) {
      this.performanceMode = 'medium';
      gsap.globalTimeline.timeScale(0.85);
    } else {
      this.performanceMode = 'high';
      gsap.globalTimeline.timeScale(1);
    }
  }

  // Setup adaptive quality based on device capabilities
  private static setupAdaptiveQuality() {
    // Check device capabilities
    const deviceMemory = (navigator as any).deviceMemory || 4;
    const hardwareConcurrency = navigator.hardwareConcurrency || 4;
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    if (isMobile || deviceMemory < 4 || hardwareConcurrency < 4) {
      this.performanceMode = 'medium';
      gsap.defaults({
        duration: 0.3,
        ease: "power1.out"
      });
    }
  }

  // Optimize for specific device types
  private static optimizeForDevice() {
    const userAgent = navigator.userAgent;
    
    // iOS optimizations
    if (/iPad|iPhone|iPod/.test(userAgent)) {
      gsap.config({
        force3D: true,
        nullTargetWarn: false,
      });
    }
    
    // Android optimizations
    if (/Android/.test(userAgent)) {
      gsap.defaults({
        duration: 0.4,
        ease: "power1.out"
      });
    }
  }

  // Create 60fps optimized animation
  static create60FPSAnimation(element: HTMLElement | string, properties: gsap.TweenVars) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    // Optimize properties for 60fps
    const optimizedProps = {
      ...properties,
      force3D: true,
      willChange: 'transform, opacity',
      ease: properties.ease || "power2.out",
      duration: this.getOptimizedDuration(properties.duration || 0.5),
    };

    // Set will-change before animation
    gsap.set(target, { willChange: 'transform, opacity' });

    const tween = gsap.to(target, {
      ...optimizedProps,
      onComplete: () => {
        // Reset will-change after animation
        gsap.set(target, { willChange: 'auto' });
        if (properties.onComplete) properties.onComplete();
      }
    });

    return tween;
  }

  // Get optimized duration based on performance mode
  private static getOptimizedDuration(duration: number): number {
    switch (this.performanceMode) {
      case 'low':
        return duration * 0.7;
      case 'medium':
        return duration * 0.85;
      default:
        return duration;
    }
  }

  // 60fps optimized fade in
  static fadeIn60FPS(element: HTMLElement | string, options: gsap.TweenVars = {}) {
    return this.create60FPSAnimation(element, {
      opacity: 1,
      duration: 0.5,
      ...options,
      onStart: () => {
        const target = typeof element === 'string' ? document.querySelector(element) : element;
        if (target) gsap.set(target, { opacity: 0 });
        if (options.onStart) options.onStart();
      }
    });
  }

  // 60fps optimized slide up
  static slideUp60FPS(element: HTMLElement | string, options: gsap.TweenVars = {}) {
    return this.create60FPSAnimation(element, {
      opacity: 1,
      y: 0,
      duration: 0.6,
      ...options,
      onStart: () => {
        const target = typeof element === 'string' ? document.querySelector(element) : element;
        if (target) gsap.set(target, { opacity: 0, y: 50 });
        if (options.onStart) options.onStart();
      }
    });
  }

  // 60fps optimized scale in
  static scaleIn60FPS(element: HTMLElement | string, options: gsap.TweenVars = {}) {
    return this.create60FPSAnimation(element, {
      opacity: 1,
      scale: 1,
      duration: 0.5,
      ease: "back.out(1.7)",
      ...options,
      onStart: () => {
        const target = typeof element === 'string' ? document.querySelector(element) : element;
        if (target) gsap.set(target, { opacity: 0, scale: 0.8 });
        if (options.onStart) options.onStart();
      }
    });
  }

  // 60fps optimized stagger animation
  static stagger60FPS(elements: HTMLElement[] | NodeList | string, options: gsap.TweenVars = {}) {
    const targets = typeof elements === 'string' ? document.querySelectorAll(elements) : elements;
    if (!targets || targets.length === 0) return;

    // Set initial state
    gsap.set(targets, { 
      opacity: 0, 
      y: 30,
      willChange: 'transform, opacity'
    });

    return gsap.to(targets, {
      opacity: 1,
      y: 0,
      duration: this.getOptimizedDuration(0.6),
      ease: "power2.out",
      stagger: 0.1,
      force3D: true,
      onComplete: () => {
        gsap.set(targets, { willChange: 'auto' });
        if (options.onComplete) options.onComplete();
      },
      ...options
    });
  }

  // 60fps optimized hover effect
  static hover60FPS(element: HTMLElement | string, hoverProps: gsap.TweenVars, resetProps?: gsap.TweenVars) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    let hoverTween: gsap.core.Tween;
    let resetTween: gsap.core.Tween;

    const handleMouseEnter = () => {
      if (resetTween) resetTween.kill();
      gsap.set(target, { willChange: 'transform' });
      
      hoverTween = gsap.to(target, {
        duration: 0.3,
        ease: "power2.out",
        force3D: true,
        ...hoverProps
      });
    };

    const handleMouseLeave = () => {
      if (hoverTween) hoverTween.kill();
      
      resetTween = gsap.to(target, {
        duration: 0.3,
        ease: "power2.out",
        force3D: true,
        onComplete: () => {
          gsap.set(target, { willChange: 'auto' });
        },
        ...resetProps
      });
    };

    target.addEventListener('mouseenter', handleMouseEnter);
    target.addEventListener('mouseleave', handleMouseLeave);

    return {
      destroy: () => {
        target.removeEventListener('mouseenter', handleMouseEnter);
        target.removeEventListener('mouseleave', handleMouseLeave);
        if (hoverTween) hoverTween.kill();
        if (resetTween) resetTween.kill();
        gsap.set(target, { willChange: 'auto' });
      }
    };
  }

  // Batch process animations for better performance
  static batchAnimate(animations: Array<{ element: HTMLElement | string, properties: gsap.TweenVars }>) {
    const timeline = gsap.timeline();
    
    animations.forEach(({ element, properties }, index) => {
      const target = typeof element === 'string' ? document.querySelector(element) : element;
      if (!target) return;

      timeline.to(target, {
        force3D: true,
        willChange: 'transform, opacity',
        ...properties,
        onComplete: () => {
          gsap.set(target, { willChange: 'auto' });
          if (properties.onComplete) properties.onComplete();
        }
      }, index * 0.1);
    });

    return timeline;
  }

  // Get current performance stats
  static getPerformanceStats() {
    return {
      actualFPS: this.actualFPS,
      performanceMode: this.performanceMode,
      timeScale: gsap.globalTimeline.timeScale(),
      activeAnimations: gsap.globalTimeline.getChildren().length
    };
  }

  // Force performance mode
  static setPerformanceMode(mode: 'high' | 'medium' | 'low') {
    this.performanceMode = mode;
    
    switch (mode) {
      case 'low':
        gsap.globalTimeline.timeScale(0.7);
        gsap.defaults({ duration: 0.3, ease: "power1.out" });
        break;
      case 'medium':
        gsap.globalTimeline.timeScale(0.85);
        gsap.defaults({ duration: 0.4, ease: "power1.out" });
        break;
      case 'high':
        gsap.globalTimeline.timeScale(1);
        gsap.defaults({ duration: 0.5, ease: "power2.out" });
        break;
    }
  }

  // Apply professional optimizations
  private static applyProfessionalOptimizations() {
    if (typeof window === 'undefined') return;

    // Optimize document for professional animations
    document.documentElement.style.setProperty('--gsap-duration', '0.4s');
    document.documentElement.style.setProperty('--gsap-ease', 'cubic-bezier(0.25, 0.1, 0.25, 1)');

    // Add professional performance CSS class to body
    document.body.classList.add('gsap-professional-mode');

    // Create professional animation styles
    const style = document.createElement('style');
    style.textContent = `
      .gsap-professional-mode {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: optimizeLegibility;
      }
      .gsap-professional-mode * {
        transform: translateZ(0);
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
      }
      .gsap-professional-mode .gsap-animate {
        will-change: transform, opacity;
      }
    `;
    document.head.appendChild(style);
  }

  // Setup smooth scrolling with GSAP
  private static setupSmoothScrolling() {
    if (typeof window === 'undefined') return;

    // Enhanced smooth scrolling
    const smoothScrollElements = document.querySelectorAll('[data-smooth-scroll]');
    smoothScrollElements.forEach(element => {
      element.addEventListener('click', (e) => {
        e.preventDefault();
        const target = document.querySelector(element.getAttribute('href') || '');
        if (target) {
          gsap.to(window, {
            duration: 1,
            scrollTo: { y: target, offsetY: 80 },
            ease: "power2.inOut"
          });
        }
      });
    });

    // Optimize scroll performance
    let ticking = false;
    const updateScrollAnimations = () => {
      // Update scroll-triggered animations here
      ticking = false;
    };

    window.addEventListener('scroll', () => {
      if (!ticking) {
        requestAnimationFrame(updateScrollAnimations);
        ticking = true;
      }
    }, { passive: true });
  }

  // Initialize animation batching system
  private static initAnimationBatching() {
    let animationQueue: Array<() => void> = [];
    let isProcessing = false;

    const processQueue = () => {
      if (animationQueue.length === 0) {
        isProcessing = false;
        return;
      }

      const batch = animationQueue.splice(0, 5); // Process 5 animations at a time
      batch.forEach(animation => animation());

      if (animationQueue.length > 0) {
        requestAnimationFrame(processQueue);
      } else {
        isProcessing = false;
      }
    };

    // Add method to queue animations
    (this as any).queueAnimation = (animation: () => void) => {
      animationQueue.push(animation);
      if (!isProcessing) {
        isProcessing = true;
        requestAnimationFrame(processQueue);
      }
    };
  }

  // Professional fade in with optimizations
  static professionalFadeIn(element: HTMLElement | string, options: gsap.TweenVars = {}) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    gsap.set(target, {
      opacity: 0,
      willChange: 'opacity',
      transform: 'translateZ(0)'
    });

    return gsap.to(target, {
      opacity: 1,
      duration: 0.6,
      ease: "power2.out",
      force3D: true,
      onComplete: () => {
        gsap.set(target, { willChange: 'auto' });
      },
      ...options
    });
  }

  // Professional slide animation
  static professionalSlide(element: HTMLElement | string, direction: 'up' | 'down' | 'left' | 'right', options: gsap.TweenVars = {}) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    const directions = {
      up: { y: 50 },
      down: { y: -50 },
      left: { x: 50 },
      right: { x: -50 }
    };

    const fromProps = { opacity: 0, ...directions[direction] };
    const toProps = { opacity: 1, x: 0, y: 0 };

    gsap.set(target, {
      ...fromProps,
      willChange: 'transform, opacity',
      transform: 'translateZ(0)'
    });

    return gsap.to(target, {
      ...toProps,
      duration: 0.8,
      ease: "power2.out",
      force3D: true,
      onComplete: () => {
        gsap.set(target, { willChange: 'auto' });
      },
      ...options
    });
  }

  // Professional stagger with enhanced timing
  static professionalStagger(elements: HTMLElement[] | NodeList | string, options: gsap.TweenVars = {}) {
    const targets = typeof elements === 'string' ? document.querySelectorAll(elements) : elements;
    if (!targets || targets.length === 0) return;

    gsap.set(targets, {
      opacity: 0,
      y: 30,
      willChange: 'transform, opacity',
      transform: 'translateZ(0)'
    });

    return gsap.to(targets, {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: "power2.out",
      stagger: {
        amount: 0.6,
        from: "start"
      },
      force3D: true,
      onComplete: () => {
        gsap.set(targets, { willChange: 'auto' });
      },
      ...options
    });
  }

  // Cleanup and reset
  static cleanup() {
    gsap.killTweensOf("*");
    gsap.globalTimeline.timeScale(1);
    this.performanceMode = 'high';
    this.initialized = false;
  }
}

// Auto-initialize
if (typeof window !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => GSAP60FPSOptimizer.init());
  } else {
    GSAP60FPSOptimizer.init();
  }
}

export default GSAP60FPSOptimizer;
