'use client'

import { motion } from 'framer-motion'
import { IconCloud } from "@/components/ui/interactive-icon-cloud"
import { useIsMobile } from '@/app/utils/deviceDetection'

const techSlugs = [
  "typescript",
  "javascript",
  "react",
  "nextdotjs",
  "nodejs",
  "express",
  "prisma",
  "postgresql",
  "mongodb",
  "firebase",
  "supabase",
  "vercel",
  "aws",
  "docker",
  "kubernetes",
  "git",
  "github",
  "figma",
  "tailwindcss",
  "sass",
  "html5",
  "css3",
  "python",
  "django",
  "fastapi",
  "redis",
  "nginx",
  "cloudflare",
  "stripe",
  "openai"
]

export default function TechStackSection() {
  const isMobile = useIsMobile()

  // Optimized animation variants for 60fps performance
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        duration: 0.6,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  }

  const floatingVariants = {
    animate: {
      y: [0, -10, 0],
      rotate: [0, 5, 0],
      transition: {
        duration: 4,
        repeat: Infinity,
        ease: "easeInOut",
        repeatType: "reverse" as const
      }
    }
  }

  return (
    <div className="w-full py-12 sm:py-16 lg:py-20 relative overflow-hidden">
      {/* Optimized background gradient with transform3d for hardware acceleration */}
      <div
        className="absolute inset-0 bg-gradient-to-b from-transparent via-purple-900/5 to-transparent"
        style={{ transform: 'translate3d(0, 0, 0)', willChange: 'transform' }}
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12 lg:gap-16 items-center"
        >

          {/* Left Column - Content */}
          <motion.div
            variants={itemVariants}
            className="space-y-6 lg:space-y-8"
          >
            <div className="space-y-4 sm:space-y-6">
              <motion.div
                variants={itemVariants}
                className="inline-block"
              >
                <span className="text-xs sm:text-sm font-medium text-purple-400 bg-purple-400/10 px-3 py-1.5 rounded-full border border-purple-400/20 backdrop-blur-sm">
                  Technology Stack
                </span>
              </motion.div>

              <motion.h2
                variants={itemVariants}
                className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white leading-tight"
                style={{ transform: 'translate3d(0, 0, 0)', willChange: 'transform' }}
              >
                Cutting-Edge
                <span className="block bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 bg-clip-text text-transparent">
                  Technologies
                </span>
              </motion.h2>

              <motion.p
                variants={itemVariants}
                className="text-base sm:text-lg text-gray-300 leading-relaxed max-w-xl"
              >
                I leverage the latest technologies and frameworks to build scalable,
                performant, and future-ready applications that drive business growth.
              </motion.p>
            </div>

            <motion.div
              variants={itemVariants}
              className={`grid ${isMobile ? 'grid-cols-1 gap-4' : 'grid-cols-2 gap-4 sm:gap-6'}`}
            >
              <motion.div
                variants={itemVariants}
                className="space-y-2 sm:space-y-3 p-3 sm:p-4 rounded-lg bg-gradient-to-br from-purple-900/10 to-transparent border border-purple-500/10 backdrop-blur-sm"
                style={{ transform: 'translate3d(0, 0, 0)', willChange: 'transform' }}
              >
                <h3 className="text-white font-semibold text-sm sm:text-base">Frontend</h3>
                <div className="space-y-1.5 sm:space-y-2 text-xs sm:text-sm text-gray-400">
                  <div className="hover:text-purple-300 transition-colors duration-200">React & Next.js</div>
                  <div className="hover:text-purple-300 transition-colors duration-200">TypeScript</div>
                  <div className="hover:text-purple-300 transition-colors duration-200">Tailwind CSS</div>
                  <div className="hover:text-purple-300 transition-colors duration-200">Framer Motion</div>
                </div>
              </motion.div>

              <motion.div
                variants={itemVariants}
                className="space-y-2 sm:space-y-3 p-3 sm:p-4 rounded-lg bg-gradient-to-br from-blue-900/10 to-transparent border border-blue-500/10 backdrop-blur-sm"
                style={{ transform: 'translate3d(0, 0, 0)', willChange: 'transform' }}
              >
                <h3 className="text-white font-semibold text-sm sm:text-base">Backend</h3>
                <div className="space-y-1.5 sm:space-y-2 text-xs sm:text-sm text-gray-400">
                  <div className="hover:text-blue-300 transition-colors duration-200">Node.js & Express</div>
                  <div className="hover:text-blue-300 transition-colors duration-200">Python & Django</div>
                  <div className="hover:text-blue-300 transition-colors duration-200">PostgreSQL & MongoDB</div>
                  <div className="hover:text-blue-300 transition-colors duration-200">Redis & Prisma</div>
                </div>
              </motion.div>

              <motion.div
                variants={itemVariants}
                className="space-y-2 sm:space-y-3 p-3 sm:p-4 rounded-lg bg-gradient-to-br from-green-900/10 to-transparent border border-green-500/10 backdrop-blur-sm"
                style={{ transform: 'translate3d(0, 0, 0)', willChange: 'transform' }}
              >
                <h3 className="text-white font-semibold text-sm sm:text-base">Cloud & DevOps</h3>
                <div className="space-y-1.5 sm:space-y-2 text-xs sm:text-sm text-gray-400">
                  <div className="hover:text-green-300 transition-colors duration-200">AWS & Vercel</div>
                  <div className="hover:text-green-300 transition-colors duration-200">Docker & Kubernetes</div>
                  <div className="hover:text-green-300 transition-colors duration-200">CI/CD Pipelines</div>
                  <div className="hover:text-green-300 transition-colors duration-200">Monitoring & Analytics</div>
                </div>
              </motion.div>

              <motion.div
                variants={itemVariants}
                className="space-y-2 sm:space-y-3 p-3 sm:p-4 rounded-lg bg-gradient-to-br from-pink-900/10 to-transparent border border-pink-500/10 backdrop-blur-sm"
                style={{ transform: 'translate3d(0, 0, 0)', willChange: 'transform' }}
              >
                <h3 className="text-white font-semibold text-sm sm:text-base">AI & Tools</h3>
                <div className="space-y-1.5 sm:space-y-2 text-xs sm:text-sm text-gray-400">
                  <div className="hover:text-pink-300 transition-colors duration-200">OpenAI Integration</div>
                  <div className="hover:text-pink-300 transition-colors duration-200">Machine Learning</div>
                  <div className="hover:text-pink-300 transition-colors duration-200">Design Systems</div>
                  <div className="hover:text-pink-300 transition-colors duration-200">Performance Optimization</div>
                </div>
              </motion.div>
            </motion.div>
          </motion.div>

          {/* Right Column - Interactive Icon Cloud */}
          <motion.div
            variants={itemVariants}
            className="flex justify-center lg:justify-end order-first lg:order-last"
          >
            <div className={`relative w-full ${isMobile ? 'max-w-sm' : 'max-w-lg'}`}>
              {/* Optimized glow effect with hardware acceleration */}
              <div
                className="absolute inset-0 bg-gradient-to-r from-purple-500/20 via-pink-500/20 to-red-500/20 rounded-full blur-3xl"
                style={{ transform: 'translate3d(0, 0, 0)', willChange: 'transform' }}
              />

              {/* Icon Cloud Container with optimized styling */}
              <div
                className={`relative bg-gradient-to-br from-gray-900/50 to-black/50 backdrop-blur-sm rounded-2xl border border-white/10 shadow-2xl ${
                  isMobile ? 'p-4' : 'p-6 lg:p-8'
                }`}
                style={{ transform: 'translate3d(0, 0, 0)', willChange: 'transform' }}
              >
                <IconCloud iconSlugs={techSlugs} />
              </div>

              {/* Optimized floating elements with reduced complexity for mobile */}
              {!isMobile && (
                <>
                  <motion.div
                    variants={floatingVariants}
                    animate="animate"
                    className="absolute -top-4 -right-4 w-8 h-8 bg-purple-500/20 rounded-full blur-sm"
                    style={{ transform: 'translate3d(0, 0, 0)', willChange: 'transform' }}
                  />

                  <motion.div
                    variants={{
                      animate: {
                        y: [0, 10, 0],
                        rotate: [0, -5, 0],
                        transition: {
                          duration: 3,
                          repeat: Infinity,
                          ease: "easeInOut",
                          delay: 1,
                          repeatType: "reverse" as const
                        }
                      }
                    }}
                    animate="animate"
                    className="absolute -bottom-4 -left-4 w-6 h-6 bg-pink-500/20 rounded-full blur-sm"
                    style={{ transform: 'translate3d(0, 0, 0)', willChange: 'transform' }}
                  />
                </>
              )}
            </div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  )
}
