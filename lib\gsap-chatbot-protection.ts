'use client';

import { gsap } from 'gsap';

// GSAP Chatbot Protection - Ensures chatbot functionality is preserved
export class GSAPChatbotProtection {
  private static protectedSelectors = [
    '[data-chatbot]',
    '.nexious-chatbot',
    '.chatbot-container',
    '.chatbot-wrapper',
    '#nexious-chatbot',
    '.sticky-chatbot',
    '[class*="chatbot"]',
    '[id*="chatbot"]'
  ];

  private static protectedElements: Set<Element> = new Set();

  // Initialize chatbot protection
  static init() {
    if (typeof window === 'undefined') return;

    // Find and protect chatbot elements
    this.identifyAndProtectChatbotElements();
    
    // Setup mutation observer to protect dynamically added chatbot elements
    this.setupChatbotProtection();
    
    // Override GSAP methods to exclude chatbot elements
    this.overrideGSAPMethods();
  }

  // Identify and protect chatbot elements
  private static identifyAndProtectChatbotElements() {
    this.protectedSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        this.protectedElements.add(element);
        this.applyChatbotProtection(element as HTMLElement);
      });
    });
  }

  // Apply protection to a chatbot element
  private static applyChatbotProtection(element: HTMLElement) {
    // Add protection attributes
    element.setAttribute('data-gsap-protected', 'true');
    element.setAttribute('data-chatbot-protected', 'true');
    
    // Ensure sticky positioning is preserved
    const computedStyle = window.getComputedStyle(element);
    if (computedStyle.position === 'fixed' || computedStyle.position === 'sticky') {
      element.style.position = computedStyle.position;
      element.style.zIndex = computedStyle.zIndex || '9999';
    }
    
    // Prevent GSAP from modifying critical chatbot styles
    element.style.setProperty('transform', 'none', 'important');
    element.style.setProperty('will-change', 'auto', 'important');
    
    // Protect child elements
    const children = element.querySelectorAll('*');
    children.forEach(child => {
      (child as HTMLElement).setAttribute('data-gsap-protected', 'true');
    });
  }

  // Setup mutation observer for dynamic chatbot elements
  private static setupChatbotProtection() {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach(mutation => {
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as HTMLElement;
            
            // Check if it's a chatbot element
            if (this.isChatbotElement(element)) {
              this.protectedElements.add(element);
              this.applyChatbotProtection(element);
            }
            
            // Check children for chatbot elements
            this.protectedSelectors.forEach(selector => {
              const chatbotElements = element.querySelectorAll(selector);
              chatbotElements.forEach(chatbotEl => {
                this.protectedElements.add(chatbotEl);
                this.applyChatbotProtection(chatbotEl as HTMLElement);
              });
            });
          }
        });
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class', 'id']
    });
  }

  // Check if an element is a chatbot element
  private static isChatbotElement(element: HTMLElement): boolean {
    const className = element.className.toLowerCase();
    const id = element.id.toLowerCase();
    const dataAttributes = Array.from(element.attributes)
      .map(attr => attr.name.toLowerCase())
      .join(' ');

    const chatbotKeywords = ['chatbot', 'nexious', 'chat', 'bot', 'assistant'];
    
    return chatbotKeywords.some(keyword => 
      className.includes(keyword) || 
      id.includes(keyword) || 
      dataAttributes.includes(keyword)
    );
  }

  // Override GSAP methods to exclude protected elements
  private static overrideGSAPMethods() {
    const originalTo = gsap.to;
    const originalFrom = gsap.from;
    const originalFromTo = gsap.fromTo;
    const originalSet = gsap.set;

    // Override gsap.to
    gsap.to = function(targets: any, vars: any) {
      const filteredTargets = GSAPChatbotProtection.filterProtectedElements(targets);
      if (filteredTargets === null) return gsap.timeline(); // Return empty timeline
      return originalTo.call(this, filteredTargets, vars);
    };

    // Override gsap.from
    gsap.from = function(targets: any, vars: any) {
      const filteredTargets = GSAPChatbotProtection.filterProtectedElements(targets);
      if (filteredTargets === null) return gsap.timeline();
      return originalFrom.call(this, filteredTargets, vars);
    };

    // Override gsap.fromTo
    gsap.fromTo = function(targets: any, fromVars: any, toVars: any) {
      const filteredTargets = GSAPChatbotProtection.filterProtectedElements(targets);
      if (filteredTargets === null) return gsap.timeline();
      return originalFromTo.call(this, filteredTargets, fromVars, toVars);
    };

    // Override gsap.set
    gsap.set = function(targets: any, vars: any) {
      const filteredTargets = GSAPChatbotProtection.filterProtectedElements(targets);
      if (filteredTargets === null) return;
      return originalSet.call(this, filteredTargets, vars);
    };
  }

  // Filter out protected elements from GSAP targets
  private static filterProtectedElements(targets: any): any {
    if (!targets) return targets;

    // Handle string selectors
    if (typeof targets === 'string') {
      const elements = Array.from(document.querySelectorAll(targets));
      const filtered = elements.filter(el => !this.isProtectedElement(el));
      return filtered.length > 0 ? filtered : null;
    }

    // Handle NodeList or Array
    if (targets.length !== undefined) {
      const filtered = Array.from(targets).filter(el => !this.isProtectedElement(el));
      return filtered.length > 0 ? filtered : null;
    }

    // Handle single element
    if (targets.nodeType) {
      return this.isProtectedElement(targets) ? null : targets;
    }

    return targets;
  }

  // Check if an element is protected
  private static isProtectedElement(element: any): boolean {
    if (!element || !element.nodeType) return false;

    // Check if element is in protected set
    if (this.protectedElements.has(element)) return true;

    // Check if element has protection attributes
    if (element.hasAttribute && element.hasAttribute('data-gsap-protected')) return true;

    // Check if element is inside a protected container
    let parent = element.parentElement;
    while (parent) {
      if (this.protectedElements.has(parent) || 
          parent.hasAttribute('data-gsap-protected')) {
        return true;
      }
      parent = parent.parentElement;
    }

    return false;
  }

  // Manually protect an element
  static protectElement(element: HTMLElement | string) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    this.protectedElements.add(target);
    this.applyChatbotProtection(target as HTMLElement);
  }

  // Unprotect an element
  static unprotectElement(element: HTMLElement | string) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    this.protectedElements.delete(target);
    target.removeAttribute('data-gsap-protected');
    target.removeAttribute('data-chatbot-protected');
  }

  // Get list of protected elements
  static getProtectedElements(): Element[] {
    return Array.from(this.protectedElements);
  }

  // Check if chatbot is functioning properly
  static verifyChatbotFunctionality(): boolean {
    const chatbotElements = Array.from(this.protectedElements);
    
    return chatbotElements.every(element => {
      const htmlElement = element as HTMLElement;
      
      // Check if element is still in DOM
      if (!document.contains(htmlElement)) return false;
      
      // Check if positioning is preserved
      const style = window.getComputedStyle(htmlElement);
      if (style.position === 'fixed' || style.position === 'sticky') {
        return true;
      }
      
      return true;
    });
  }

  // Restore chatbot functionality if compromised
  static restoreChatbotFunctionality() {
    this.protectedElements.forEach(element => {
      this.applyChatbotProtection(element as HTMLElement);
    });
  }

  // Cleanup protection
  static cleanup() {
    this.protectedElements.clear();
    
    // Remove protection attributes
    document.querySelectorAll('[data-gsap-protected]').forEach(element => {
      element.removeAttribute('data-gsap-protected');
      element.removeAttribute('data-chatbot-protected');
    });
  }
}

// Auto-initialize chatbot protection
if (typeof window !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => GSAPChatbotProtection.init());
  } else {
    GSAPChatbotProtection.init();
  }
}

export default GSAPChatbotProtection;
