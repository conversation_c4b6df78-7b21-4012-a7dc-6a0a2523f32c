'use client';

import { gsap } from 'gsap';

// GSAP Memory Optimizer - Prevents memory leaks and optimizes performance
export class GSAPMemoryOptimizer {
  private static initialized = false;
  private static activeAnimations: Set<gsap.core.Tween | gsap.core.Timeline> = new Set();
  private static elementAnimations: Map<Element, gsap.core.Tween[]> = new Map();
  private static cleanupInterval: number | null = null;
  private static memoryThreshold = 50 * 1024 * 1024; // 50MB threshold

  // Initialize memory optimization
  static init() {
    if (this.initialized || typeof window === 'undefined') return;

    // Setup automatic cleanup
    this.setupAutomaticCleanup();
    
    // Setup memory monitoring
    this.setupMemoryMonitoring();
    
    // Setup visibility change cleanup
    this.setupVisibilityCleanup();

    this.initialized = true;
  }

  // Setup automatic cleanup interval
  private static setupAutomaticCleanup() {
    this.cleanupInterval = window.setInterval(() => {
      this.performCleanup();
    }, 30000); // Cleanup every 30 seconds
  }

  // Setup memory monitoring
  private static setupMemoryMonitoring() {
    if (!(performance as any).memory) return;

    const checkMemory = () => {
      const memoryInfo = (performance as any).memory;
      if (memoryInfo.usedJSHeapSize > this.memoryThreshold) {
        console.warn('High memory usage detected, performing aggressive cleanup');
        this.performAggressiveCleanup();
      }
    };

    // Check memory every 10 seconds
    setInterval(checkMemory, 10000);
  }

  // Setup cleanup on visibility change
  private static setupVisibilityCleanup() {
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        // Page is hidden, perform cleanup
        this.performCleanup();
      }
    });
  }

  // Register animation for tracking
  static registerAnimation(animation: gsap.core.Tween | gsap.core.Timeline, element?: Element) {
    this.activeAnimations.add(animation);
    
    if (element) {
      if (!this.elementAnimations.has(element)) {
        this.elementAnimations.set(element, []);
      }
      this.elementAnimations.get(element)!.push(animation as gsap.core.Tween);
    }

    // Add cleanup callback
    animation.eventCallback('onComplete', () => {
      this.unregisterAnimation(animation);
    });
  }

  // Unregister animation
  static unregisterAnimation(animation: gsap.core.Tween | gsap.core.Timeline) {
    this.activeAnimations.delete(animation);
    
    // Remove from element animations
    this.elementAnimations.forEach((animations, element) => {
      const index = animations.indexOf(animation as gsap.core.Tween);
      if (index > -1) {
        animations.splice(index, 1);
        if (animations.length === 0) {
          this.elementAnimations.delete(element);
        }
      }
    });
  }

  // Perform regular cleanup
  private static performCleanup() {
    let cleanedCount = 0;

    // Clean up completed animations
    this.activeAnimations.forEach(animation => {
      if (!animation.isActive() && animation.progress() === 1) {
        animation.kill();
        this.activeAnimations.delete(animation);
        cleanedCount++;
      }
    });

    // Clean up animations for removed elements
    this.elementAnimations.forEach((animations, element) => {
      if (!document.contains(element)) {
        animations.forEach(animation => {
          animation.kill();
          this.activeAnimations.delete(animation);
          cleanedCount++;
        });
        this.elementAnimations.delete(element);
      }
    });

    // Reset will-change properties on inactive elements
    this.resetWillChangeProperties();

    if (cleanedCount > 0) {
      console.log(`GSAP Memory Optimizer: Cleaned up ${cleanedCount} animations`);
    }
  }

  // Perform aggressive cleanup
  private static performAggressiveCleanup() {
    // Kill all non-essential animations
    this.activeAnimations.forEach(animation => {
      if (!animation.isActive()) {
        animation.kill();
        this.activeAnimations.delete(animation);
      }
    });

    // Clear element animations map
    this.elementAnimations.clear();

    // Force garbage collection if available
    if ((window as any).gc) {
      (window as any).gc();
    }

    // Reset all will-change properties
    document.querySelectorAll('[style*="will-change"]').forEach(element => {
      (element as HTMLElement).style.willChange = 'auto';
    });

    console.log('GSAP Memory Optimizer: Performed aggressive cleanup');
  }

  // Reset will-change properties
  private static resetWillChangeProperties() {
    document.querySelectorAll('[style*="will-change"]').forEach(element => {
      const htmlElement = element as HTMLElement;
      const rect = htmlElement.getBoundingClientRect();
      
      // If element is not visible, reset will-change
      if (rect.bottom < 0 || rect.top > window.innerHeight) {
        htmlElement.style.willChange = 'auto';
      }
    });
  }

  // Create optimized animation with automatic cleanup
  static createOptimizedAnimation(
    target: gsap.TweenTarget, 
    vars: gsap.TweenVars,
    type: 'to' | 'from' | 'fromTo' = 'to',
    fromVars?: gsap.TweenVars
  ) {
    let animation: gsap.core.Tween;

    // Add memory optimization properties
    const optimizedVars = {
      ...vars,
      force3D: true,
      onStart: () => {
        // Set will-change at start
        gsap.set(target, { willChange: 'transform, opacity' });
        if (vars.onStart) vars.onStart();
      },
      onComplete: () => {
        // Reset will-change on complete
        gsap.set(target, { willChange: 'auto' });
        if (vars.onComplete) vars.onComplete();
      }
    };

    // Create animation based on type
    switch (type) {
      case 'from':
        animation = gsap.from(target, optimizedVars);
        break;
      case 'fromTo':
        animation = gsap.fromTo(target, fromVars || {}, optimizedVars);
        break;
      default:
        animation = gsap.to(target, optimizedVars);
    }

    // Register for tracking
    const element = typeof target === 'string' ? document.querySelector(target) : 
                   target instanceof Array ? target[0] as Element :
                   target as Element;
    
    if (element) {
      this.registerAnimation(animation, element);
    }

    return animation;
  }

  // Create optimized timeline with automatic cleanup
  static createOptimizedTimeline(vars?: gsap.TimelineVars) {
    const timeline = gsap.timeline({
      ...vars,
      onComplete: () => {
        // Auto cleanup on complete
        this.unregisterAnimation(timeline);
        if (vars?.onComplete) vars.onComplete();
      }
    });

    this.registerAnimation(timeline);
    return timeline;
  }

  // Batch kill animations for specific elements
  static killAnimationsForElement(element: Element) {
    const animations = this.elementAnimations.get(element);
    if (animations) {
      animations.forEach(animation => {
        animation.kill();
        this.activeAnimations.delete(animation);
      });
      this.elementAnimations.delete(element);
    }

    // Reset element properties
    gsap.set(element, { 
      willChange: 'auto',
      clearProps: 'all'
    });
  }

  // Get memory statistics
  static getMemoryStats() {
    const memoryInfo = (performance as any).memory;
    
    return {
      activeAnimations: this.activeAnimations.size,
      trackedElements: this.elementAnimations.size,
      memoryUsage: memoryInfo ? {
        used: Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024),
        total: Math.round(memoryInfo.totalJSHeapSize / 1024 / 1024),
        limit: Math.round(memoryInfo.jsHeapSizeLimit / 1024 / 1024)
      } : null
    };
  }

  // Force cleanup all animations
  static forceCleanupAll() {
    this.activeAnimations.forEach(animation => animation.kill());
    this.activeAnimations.clear();
    this.elementAnimations.clear();
    
    // Reset all GSAP properties
    gsap.set('*', { clearProps: 'all' });
    
    // Reset all will-change properties
    document.querySelectorAll('[style*="will-change"]').forEach(element => {
      (element as HTMLElement).style.willChange = 'auto';
    });
  }

  // Optimize for page unload
  static optimizeForUnload() {
    // Kill all animations immediately
    gsap.killTweensOf('*');
    
    // Clear all tracking
    this.activeAnimations.clear();
    this.elementAnimations.clear();
    
    // Clear cleanup interval
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }

  // Cleanup and reset
  static cleanup() {
    this.optimizeForUnload();
    this.initialized = false;
  }
}

// Setup page unload optimization
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    GSAPMemoryOptimizer.optimizeForUnload();
  });

  // Auto-initialize
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => GSAPMemoryOptimizer.init());
  } else {
    GSAPMemoryOptimizer.init();
  }
}

export default GSAPMemoryOptimizer;
